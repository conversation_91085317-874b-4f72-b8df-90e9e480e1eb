/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 90001
 Source Host           : localhost:3306
 Source Schema         : softwarecup

 Target Server Type    : MySQL
 Target Server Version : 90001
 File Encoding         : 65001

 Date: 23/07/2025 05:15:24
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for interviews
-- ----------------------------
DROP TABLE IF EXISTS `interviews`;
CREATE TABLE `interviews`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '面试ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `resume_id` int NULL DEFAULT NULL COMMENT '关联简历ID',
  `interview_type` enum('technical','behavioral','comprehensive','custom') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '面试类型',
  `interview_mode` enum('technical','pressure','case','comprehensive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'technical' COMMENT '面试模式',
  `position` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标岗位',
  `company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标公司',
  `difficulty_level` enum('primary','middle','high') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'middle' COMMENT '难度级别',
  `question_count` tinyint NULL DEFAULT 5 COMMENT '问题数量',
  `time_limit` int NULL DEFAULT NULL COMMENT '时间限制(分钟)',
  `status` enum('scheduled','in_progress','completed','cancelled','paused') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'scheduled' COMMENT '面试状态',
  `scheduled_at` timestamp NULL DEFAULT NULL COMMENT '计划时间',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `duration` int NULL DEFAULT NULL COMMENT '实际时长(秒)',
  `interviewer_expression` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'friendly' COMMENT '面试官表情: friendly, serious, pressure',
  `interaction_mode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'frequent' COMMENT '互动模式: frequent, listener, counter',
  `voice_speed` decimal(3, 1) NULL DEFAULT 1.0 COMMENT '语音速度 (0.5-2.0)',
  `enable_emotion_feedback` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用微表情反馈',
  `feedback_types` json NULL COMMENT '反馈类型: [\"nod\", \"frown\", \"timer\"]',
  `recording_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'video' COMMENT '录制类型: video, audio',
  `ai_highlight` tinyint(1) NULL DEFAULT 1 COMMENT 'AI高亮标记',
  `improvement_marking` tinyint(1) NULL DEFAULT 1 COMMENT '改进点标记',
  `background_noises` json NULL COMMENT '背景噪音设置',
  `total_questions` int NULL DEFAULT 0 COMMENT '总问题数',
  `answered_questions` int NULL DEFAULT 0 COMMENT '已回答问题数',
  `questions_data` json NULL COMMENT '面试问题数据',
  `transcriptions_data` json NULL COMMENT '语音转写数据',
  `ai_evaluations_data` json NULL COMMENT 'AI评估数据',
  `real_time_feedback_data` json NULL COMMENT '实时反馈数据',
  `interview_config` json NULL COMMENT '面试配置',
  `overall_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '综合评分',
  `technical_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '技术能力评分',
  `expression_logic_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '表达逻辑评分',
  `adaptability_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '应变能力评分',
  `behavior_etiquette_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '行为礼仪评分',
  `english_communication_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '英语沟通评分',
  `cultural_fit_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '文化匹配评分',
  `report_data` json NULL COMMENT '面试报告数据',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_resume_id`(`resume_id`) USING BTREE,
  INDEX `idx_interview_type`(`interview_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `interviews_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `interviews_ibfk_2` FOREIGN KEY (`resume_id`) REFERENCES `resumes` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 60 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '面试记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of interviews
-- ----------------------------
INSERT INTO `interviews` VALUES (1, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Java\", \"网络综合布线\", \"Dreamweaver\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"C语言\", \"ASP\", \"CAD\", \"DELPHI\", \"网络安全\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"网络工程师\", \"测试工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Java\", \"网络综合布线\", \"Dreamweaver\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"C语言\", \"ASP\", \"CAD\", \"DELPHI\", \"网络安全\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-20 18:57:12', '2025-07-20 18:57:12');
INSERT INTO `interviews` VALUES (2, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"网络综合布线\", \"DELPHI\", \"汇编语言与接口技术\", \"ASP\", \"C语言\", \"Pro/Engineer (proe)\", \"CAD\", \"Java\", \"网络安全\", \"Dreamweaver\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"测试工程师\", \"软件开发工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"网络综合布线\", \"DELPHI\", \"汇编语言与接口技术\", \"ASP\", \"C语言\", \"Pro/Engineer (proe)\", \"CAD\", \"Java\", \"网络安全\", \"Dreamweaver\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 03:23:00', '2025-07-21 03:23:00');
INSERT INTO `interviews` VALUES (3, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"汇编语言与接口技术\", \"网络安全\", \"C语言\", \"ASP\", \"网络综合布线\", \"CAD\", \"Java\", \"Pro/Engineer (proe)\", \"DELPHI\", \"Dreamweaver\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"技术支持工程师\", \"软件开发工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"汇编语言与接口技术\", \"网络安全\", \"C语言\", \"ASP\", \"网络综合布线\", \"CAD\", \"Java\", \"Pro/Engineer (proe)\", \"DELPHI\", \"Dreamweaver\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 03:25:39', '2025-07-21 03:25:39');
INSERT INTO `interviews` VALUES (4, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"汇编语言与接口技术\", \"网络安全\", \"C语言\", \"ASP\", \"网络综合布线\", \"CAD\", \"Java\", \"Pro/Engineer (proe)\", \"DELPHI\", \"Dreamweaver\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"技术支持工程师\", \"软件开发工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"汇编语言与接口技术\", \"网络安全\", \"C语言\", \"ASP\", \"网络综合布线\", \"CAD\", \"Java\", \"Pro/Engineer (proe)\", \"DELPHI\", \"Dreamweaver\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 03:55:38', '2025-07-21 03:55:38');
INSERT INTO `interviews` VALUES (5, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"汇编语言与接口技术\", \"网络综合布线\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"CAD\", \"DELPHI\", \"ASP\", \"C语言\", \"Java\", \"网络安全\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"网络工程师\", \"软件开发工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"汇编语言与接口技术\", \"网络综合布线\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"CAD\", \"DELPHI\", \"ASP\", \"C语言\", \"Java\", \"网络安全\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 04:08:16', '2025-07-21 04:08:16');
INSERT INTO `interviews` VALUES (6, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"C语言\", \"Java\", \"CAD\", \"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"ASP\", \"DELPHI\", \"网络综合布线\", \"Dreamweaver\", \"网络安全\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"测试工程师\", \"软件开发工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"C语言\", \"Java\", \"CAD\", \"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"ASP\", \"DELPHI\", \"网络综合布线\", \"Dreamweaver\", \"网络安全\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 04:14:08', '2025-07-21 04:14:08');
INSERT INTO `interviews` VALUES (7, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"CAD\", \"Dreamweaver\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"网络安全\", \"DELPHI\", \"Java\", \"网络综合布线\", \"ASP\", \"C语言\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"软件开发工程师\", \"网络工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"CAD\", \"Dreamweaver\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"网络安全\", \"DELPHI\", \"Java\", \"网络综合布线\", \"ASP\", \"C语言\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 04:21:42', '2025-07-21 04:21:42');
INSERT INTO `interviews` VALUES (8, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Java\", \"网络安全\", \"ASP\", \"DELPHI\", \"Pro/Engineer (proe)\", \"网络综合布线\", \"CAD\", \"Dreamweaver\", \"C语言\", \"汇编语言与接口技术\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"技术支持工程师\", \"测试工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Java\", \"网络安全\", \"ASP\", \"DELPHI\", \"Pro/Engineer (proe)\", \"网络综合布线\", \"CAD\", \"Dreamweaver\", \"C语言\", \"汇编语言与接口技术\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 05:56:38', '2025-07-21 05:56:38');
INSERT INTO `interviews` VALUES (9, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Java\", \"网络安全\", \"ASP\", \"C语言\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"网络综合布线\", \"CAD\", \"DELPHI\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"技术支持工程师\", \"网络工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Java\", \"网络安全\", \"ASP\", \"C语言\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"网络综合布线\", \"CAD\", \"DELPHI\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 06:07:36', '2025-07-21 06:07:36');
INSERT INTO `interviews` VALUES (10, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Java\", \"Pro/Engineer (proe)\", \"C语言\", \"网络综合布线\", \"ASP\", \"网络安全\", \"Dreamweaver\", \"CAD\", \"DELPHI\", \"汇编语言与接口技术\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"软件开发工程师\", \"测试工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Java\", \"Pro/Engineer (proe)\", \"C语言\", \"网络综合布线\", \"ASP\", \"网络安全\", \"Dreamweaver\", \"CAD\", \"DELPHI\", \"汇编语言与接口技术\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 07:59:35', '2025-07-21 07:59:35');
INSERT INTO `interviews` VALUES (11, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Java\", \"Pro/Engineer (proe)\", \"C语言\", \"ASP\", \"汇编语言与接口技术\", \"网络综合布线\", \"DELPHI\", \"CAD\", \"网络安全\", \"Dreamweaver\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"测试工程师\", \"网络工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Java\", \"Pro/Engineer (proe)\", \"C语言\", \"ASP\", \"汇编语言与接口技术\", \"网络综合布线\", \"DELPHI\", \"CAD\", \"网络安全\", \"Dreamweaver\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 08:22:07', '2025-07-21 08:22:07');
INSERT INTO `interviews` VALUES (12, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"网络安全\", \"Java\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"网络综合布线\", \"DELPHI\", \"汇编语言与接口技术\", \"CAD\", \"ASP\", \"C语言\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"技术支持工程师\", \"测试工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"网络安全\", \"Java\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"网络综合布线\", \"DELPHI\", \"汇编语言与接口技术\", \"CAD\", \"ASP\", \"C语言\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 09:19:56', '2025-07-21 09:19:56');
INSERT INTO `interviews` VALUES (13, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"DELPHI\", \"网络综合布线\", \"CAD\", \"网络安全\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"C语言\", \"ASP\", \"汇编语言与接口技术\", \"Java\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"网络工程师\", \"软件开发工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"DELPHI\", \"网络综合布线\", \"CAD\", \"网络安全\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"C语言\", \"ASP\", \"汇编语言与接口技术\", \"Java\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 11:24:33', '2025-07-21 11:24:33');
INSERT INTO `interviews` VALUES (14, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"CAD\", \"Pro/Engineer (proe)\", \"C语言\", \"网络综合布线\", \"Dreamweaver\", \"DELPHI\", \"ASP\", \"网络安全\", \"汇编语言与接口技术\", \"Java\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"测试工程师\", \"软件开发工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"CAD\", \"Pro/Engineer (proe)\", \"C语言\", \"网络综合布线\", \"Dreamweaver\", \"DELPHI\", \"ASP\", \"网络安全\", \"汇编语言与接口技术\", \"Java\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 11:52:43', '2025-07-21 11:52:43');
INSERT INTO `interviews` VALUES (15, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"C语言\", \"网络安全\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"网络综合布线\", \"ASP\", \"DELPHI\", \"汇编语言与接口技术\", \"CAD\", \"Java\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"技术支持工程师\", \"网络工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"C语言\", \"网络安全\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"网络综合布线\", \"ASP\", \"DELPHI\", \"汇编语言与接口技术\", \"CAD\", \"Java\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 12:44:41', '2025-07-21 12:44:41');
INSERT INTO `interviews` VALUES (16, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"CAD\", \"ASP\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"网络综合布线\", \"DELPHI\", \"汇编语言与接口技术\", \"C语言\", \"网络安全\", \"Java\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"软件开发工程师\", \"网络工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"CAD\", \"ASP\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"网络综合布线\", \"DELPHI\", \"汇编语言与接口技术\", \"C语言\", \"网络安全\", \"Java\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 13:40:58', '2025-07-21 13:40:58');
INSERT INTO `interviews` VALUES (17, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Dreamweaver\", \"DELPHI\", \"Java\", \"网络安全\", \"C语言\", \"CAD\", \"ASP\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"网络综合布线\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"网络工程师\", \"测试工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Dreamweaver\", \"DELPHI\", \"Java\", \"网络安全\", \"C语言\", \"CAD\", \"ASP\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"网络综合布线\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 13:57:22', '2025-07-21 13:57:22');
INSERT INTO `interviews` VALUES (18, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"ASP\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"C语言\", \"网络安全\", \"Java\", \"DELPHI\", \"网络综合布线\", \"Dreamweaver\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"软件开发工程师\", \"测试工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"ASP\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"C语言\", \"网络安全\", \"Java\", \"DELPHI\", \"网络综合布线\", \"Dreamweaver\", \"CAD\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 14:30:58', '2025-07-21 14:30:58');
INSERT INTO `interviews` VALUES (19, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Pro/Engineer (proe)\", \"Java\", \"网络安全\", \"Dreamweaver\", \"网络综合布线\", \"汇编语言与接口技术\", \"DELPHI\", \"CAD\", \"C语言\", \"ASP\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"网络工程师\", \"软件开发工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Pro/Engineer (proe)\", \"Java\", \"网络安全\", \"Dreamweaver\", \"网络综合布线\", \"汇编语言与接口技术\", \"DELPHI\", \"CAD\", \"C语言\", \"ASP\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 15:05:03', '2025-07-21 15:05:03');
INSERT INTO `interviews` VALUES (20, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"ASP\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"网络综合布线\", \"DELPHI\", \"Dreamweaver\", \"CAD\", \"网络安全\", \"Java\", \"C语言\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"软件开发工程师\", \"技术支持工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"ASP\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"网络综合布线\", \"DELPHI\", \"Dreamweaver\", \"CAD\", \"网络安全\", \"Java\", \"C语言\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 15:39:28', '2025-07-21 15:39:28');
INSERT INTO `interviews` VALUES (21, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"网络安全\", \"C语言\", \"Pro/Engineer (proe)\", \"网络综合布线\", \"ASP\", \"CAD\", \"Dreamweaver\", \"Java\", \"DELPHI\", \"汇编语言与接口技术\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"技术支持工程师\", \"软件开发工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"网络安全\", \"C语言\", \"Pro/Engineer (proe)\", \"网络综合布线\", \"ASP\", \"CAD\", \"Dreamweaver\", \"Java\", \"DELPHI\", \"汇编语言与接口技术\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 15:56:59', '2025-07-21 15:56:59');
INSERT INTO `interviews` VALUES (22, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"汇编语言与接口技术\", \"ASP\", \"Dreamweaver\", \"DELPHI\", \"网络安全\", \"Pro/Engineer (proe)\", \"C语言\", \"CAD\", \"网络综合布线\", \"Java\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"测试工程师\", \"网络工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"汇编语言与接口技术\", \"ASP\", \"Dreamweaver\", \"DELPHI\", \"网络安全\", \"Pro/Engineer (proe)\", \"C语言\", \"CAD\", \"网络综合布线\", \"Java\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 16:29:42', '2025-07-21 16:29:42');
INSERT INTO `interviews` VALUES (23, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Pro/Engineer (proe)\", \"CAD\", \"Java\", \"DELPHI\", \"C语言\", \"网络安全\", \"Dreamweaver\", \"网络综合布线\", \"ASP\", \"汇编语言与接口技术\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Pro/Engineer (proe)\", \"CAD\", \"Java\", \"DELPHI\", \"C语言\", \"网络安全\", \"Dreamweaver\", \"网络综合布线\", \"ASP\", \"汇编语言与接口技术\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 16:58:04', '2025-07-21 16:58:04');
INSERT INTO `interviews` VALUES (24, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"网络综合布线\", \"Dreamweaver\", \"汇编语言与接口技术\", \"Java\", \"ASP\", \"DELPHI\", \"CAD\", \"Pro/Engineer (proe)\", \"C语言\", \"网络安全\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"技术支持工程师\", \"测试工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"网络综合布线\", \"Dreamweaver\", \"汇编语言与接口技术\", \"Java\", \"ASP\", \"DELPHI\", \"CAD\", \"Pro/Engineer (proe)\", \"C语言\", \"网络安全\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 17:21:50', '2025-07-21 17:21:50');
INSERT INTO `interviews` VALUES (25, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"网络综合布线\", \"CAD\", \"汇编语言与接口技术\", \"DELPHI\", \"网络安全\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"ASP\", \"C语言\", \"Java\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"软件开发工程师\", \"网络工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"网络综合布线\", \"CAD\", \"汇编语言与接口技术\", \"DELPHI\", \"网络安全\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"ASP\", \"C语言\", \"Java\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 18:05:00', '2025-07-21 18:05:00');
INSERT INTO `interviews` VALUES (26, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"DELPHI\", \"网络综合布线\", \"Pro/Engineer (proe)\", \"ASP\", \"CAD\", \"网络安全\", \"汇编语言与接口技术\", \"Java\", \"C语言\", \"Dreamweaver\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"测试工程师\", \"软件开发工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"DELPHI\", \"网络综合布线\", \"Pro/Engineer (proe)\", \"ASP\", \"CAD\", \"网络安全\", \"汇编语言与接口技术\", \"Java\", \"C语言\", \"Dreamweaver\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 18:16:09', '2025-07-21 18:16:09');
INSERT INTO `interviews` VALUES (27, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"CAD\", \"ASP\", \"网络安全\", \"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"Java\", \"DELPHI\", \"Dreamweaver\", \"C语言\", \"网络综合布线\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"软件开发工程师\", \"技术支持工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"CAD\", \"ASP\", \"网络安全\", \"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"Java\", \"DELPHI\", \"Dreamweaver\", \"C语言\", \"网络综合布线\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 18:27:56', '2025-07-21 18:27:56');
INSERT INTO `interviews` VALUES (28, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"CAD\", \"ASP\", \"网络安全\", \"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"Java\", \"DELPHI\", \"Dreamweaver\", \"C语言\", \"网络综合布线\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"软件开发工程师\", \"技术支持工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"CAD\", \"ASP\", \"网络安全\", \"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"Java\", \"DELPHI\", \"Dreamweaver\", \"C语言\", \"网络综合布线\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 18:31:10', '2025-07-21 18:31:10');
INSERT INTO `interviews` VALUES (29, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"汇编语言与接口技术\", \"Dreamweaver\", \"CAD\", \"Pro/Engineer (proe)\", \"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"网络综合布线\", \"网络安全\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"网络工程师\", \"技术支持工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"汇编语言与接口技术\", \"Dreamweaver\", \"CAD\", \"Pro/Engineer (proe)\", \"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"网络综合布线\", \"网络安全\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 18:50:53', '2025-07-21 18:50:53');
INSERT INTO `interviews` VALUES (30, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"C语言\", \"DELPHI\", \"CAD\", \"Dreamweaver\", \"网络安全\", \"Java\", \"ASP\", \"网络综合布线\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"软件开发工程师\", \"网络工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"C语言\", \"DELPHI\", \"CAD\", \"Dreamweaver\", \"网络安全\", \"Java\", \"ASP\", \"网络综合布线\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 19:13:46', '2025-07-21 19:13:46');
INSERT INTO `interviews` VALUES (31, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Java\", \"网络安全\", \"C语言\", \"Dreamweaver\", \"DELPHI\", \"CAD\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"ASP\", \"网络综合布线\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"测试工程师\", \"软件开发工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Java\", \"网络安全\", \"C语言\", \"Dreamweaver\", \"DELPHI\", \"CAD\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"ASP\", \"网络综合布线\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 20:01:47', '2025-07-21 20:01:47');
INSERT INTO `interviews` VALUES (32, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"C语言\", \"Pro/Engineer (proe)\", \"网络安全\", \"Java\", \"网络综合布线\", \"DELPHI\", \"ASP\", \"CAD\", \"Dreamweaver\", \"汇编语言与接口技术\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"C语言\", \"Pro/Engineer (proe)\", \"网络安全\", \"Java\", \"网络综合布线\", \"DELPHI\", \"ASP\", \"CAD\", \"Dreamweaver\", \"汇编语言与接口技术\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 20:17:53', '2025-07-21 20:17:54');
INSERT INTO `interviews` VALUES (33, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"C语言\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"ASP\", \"Java\", \"DELPHI\", \"CAD\", \"网络安全\", \"网络综合布线\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"软件开发工程师\", \"测试工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"C语言\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"ASP\", \"Java\", \"DELPHI\", \"CAD\", \"网络安全\", \"网络综合布线\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 21:09:39', '2025-07-21 21:09:39');
INSERT INTO `interviews` VALUES (34, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Java\", \"Pro/Engineer (proe)\", \"C语言\", \"ASP\", \"Dreamweaver\", \"汇编语言与接口技术\", \"网络安全\", \"CAD\", \"网络综合布线\", \"DELPHI\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"软件开发工程师\", \"技术支持工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Java\", \"Pro/Engineer (proe)\", \"C语言\", \"ASP\", \"Dreamweaver\", \"汇编语言与接口技术\", \"网络安全\", \"CAD\", \"网络综合布线\", \"DELPHI\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 21:27:41', '2025-07-21 21:27:41');
INSERT INTO `interviews` VALUES (35, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"网络安全\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"Java\", \"DELPHI\", \"网络综合布线\", \"CAD\", \"ASP\", \"汇编语言与接口技术\", \"C语言\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"测试工程师\", \"软件开发工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"网络安全\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"Java\", \"DELPHI\", \"网络综合布线\", \"CAD\", \"ASP\", \"汇编语言与接口技术\", \"C语言\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 21:53:25', '2025-07-21 21:53:25');
INSERT INTO `interviews` VALUES (36, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"网络安全\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"Java\", \"DELPHI\", \"网络综合布线\", \"CAD\", \"ASP\", \"汇编语言与接口技术\", \"C语言\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"测试工程师\", \"软件开发工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"网络安全\", \"Pro/Engineer (proe)\", \"Dreamweaver\", \"Java\", \"DELPHI\", \"网络综合布线\", \"CAD\", \"ASP\", \"汇编语言与接口技术\", \"C语言\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 21:54:20', '2025-07-21 21:54:20');
INSERT INTO `interviews` VALUES (37, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Pro/Engineer (proe)\", \"Java\", \"CAD\", \"网络安全\", \"DELPHI\", \"汇编语言与接口技术\", \"Dreamweaver\", \"ASP\", \"C语言\", \"网络综合布线\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"技术支持工程师\", \"网络工程师\", \"软件开发工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Pro/Engineer (proe)\", \"Java\", \"CAD\", \"网络安全\", \"DELPHI\", \"汇编语言与接口技术\", \"Dreamweaver\", \"ASP\", \"C语言\", \"网络综合布线\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 21:57:09', '2025-07-21 21:57:09');
INSERT INTO `interviews` VALUES (38, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"ASP\", \"网络综合布线\", \"Pro/Engineer (proe)\", \"CAD\", \"C语言\", \"汇编语言与接口技术\", \"网络安全\", \"Dreamweaver\", \"Java\", \"DELPHI\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"软件开发工程师\", \"技术支持工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"ASP\", \"网络综合布线\", \"Pro/Engineer (proe)\", \"CAD\", \"C语言\", \"汇编语言与接口技术\", \"网络安全\", \"Dreamweaver\", \"Java\", \"DELPHI\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 09:36:33', '2025-07-22 09:36:33');
INSERT INTO `interviews` VALUES (39, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"ASP\", \"网络综合布线\", \"Pro/Engineer (proe)\", \"CAD\", \"C语言\", \"汇编语言与接口技术\", \"网络安全\", \"Dreamweaver\", \"Java\", \"DELPHI\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"软件开发工程师\", \"技术支持工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"ASP\", \"网络综合布线\", \"Pro/Engineer (proe)\", \"CAD\", \"C语言\", \"汇编语言与接口技术\", \"网络安全\", \"Dreamweaver\", \"Java\", \"DELPHI\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 09:36:54', '2025-07-22 09:36:54');
INSERT INTO `interviews` VALUES (40, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"CAD\", \"Pro/Engineer (proe)\", \"Java\", \"汇编语言与接口技术\", \"ASP\", \"网络安全\", \"Dreamweaver\", \"C语言\", \"网络综合布线\", \"DELPHI\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"测试工程师\", \"技术支持工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"CAD\", \"Pro/Engineer (proe)\", \"Java\", \"汇编语言与接口技术\", \"ASP\", \"网络安全\", \"Dreamweaver\", \"C语言\", \"网络综合布线\", \"DELPHI\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 12:20:08', '2025-07-22 12:20:08');
INSERT INTO `interviews` VALUES (41, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"CAD\", \"Pro/Engineer (proe)\", \"Java\", \"汇编语言与接口技术\", \"ASP\", \"网络安全\", \"Dreamweaver\", \"C语言\", \"网络综合布线\", \"DELPHI\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"测试工程师\", \"技术支持工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"CAD\", \"Pro/Engineer (proe)\", \"Java\", \"汇编语言与接口技术\", \"ASP\", \"网络安全\", \"Dreamweaver\", \"C语言\", \"网络综合布线\", \"DELPHI\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 12:23:35', '2025-07-22 12:23:35');
INSERT INTO `interviews` VALUES (42, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"ASP\", \"Pro/Engineer (proe)\", \"C语言\", \"汇编语言与接口技术\", \"网络综合布线\", \"DELPHI\", \"CAD\", \"Dreamweaver\", \"网络安全\", \"Java\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"ASP\", \"Pro/Engineer (proe)\", \"C语言\", \"汇编语言与接口技术\", \"网络综合布线\", \"DELPHI\", \"CAD\", \"Dreamweaver\", \"网络安全\", \"Java\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 14:50:09', '2025-07-22 14:50:09');
INSERT INTO `interviews` VALUES (43, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"ASP\", \"网络综合布线\", \"汇编语言与接口技术\", \"网络安全\", \"DELPHI\", \"C语言\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"Java\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"测试工程师\", \"软件开发工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"ASP\", \"网络综合布线\", \"汇编语言与接口技术\", \"网络安全\", \"DELPHI\", \"C语言\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"Java\", \"CAD\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 15:08:26', '2025-07-22 15:08:26');
INSERT INTO `interviews` VALUES (44, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Pro/Engineer (proe)\", \"CAD\", \"网络安全\", \"汇编语言与接口技术\", \"C语言\", \"Dreamweaver\", \"ASP\", \"Java\", \"DELPHI\", \"网络综合布线\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"网络工程师\", \"技术支持工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Pro/Engineer (proe)\", \"CAD\", \"网络安全\", \"汇编语言与接口技术\", \"C语言\", \"Dreamweaver\", \"ASP\", \"Java\", \"DELPHI\", \"网络综合布线\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 15:25:47', '2025-07-22 15:25:47');
INSERT INTO `interviews` VALUES (45, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"C语言\", \"网络综合布线\", \"DELPHI\", \"CAD\", \"网络安全\", \"Pro/Engineer (proe)\", \"ASP\", \"Java\", \"汇编语言与接口技术\", \"Dreamweaver\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"技术支持工程师\", \"软件开发工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"C语言\", \"网络综合布线\", \"DELPHI\", \"CAD\", \"网络安全\", \"Pro/Engineer (proe)\", \"ASP\", \"Java\", \"汇编语言与接口技术\", \"Dreamweaver\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 15:47:59', '2025-07-22 15:47:59');
INSERT INTO `interviews` VALUES (46, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"网络综合布线\", \"CAD\", \"DELPHI\", \"Pro/Engineer (proe)\", \"Java\", \"网络安全\", \"Dreamweaver\", \"ASP\", \"汇编语言与接口技术\", \"C语言\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"测试工程师\", \"网络工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"网络综合布线\", \"CAD\", \"DELPHI\", \"Pro/Engineer (proe)\", \"Java\", \"网络安全\", \"Dreamweaver\", \"ASP\", \"汇编语言与接口技术\", \"C语言\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 16:07:02', '2025-07-22 16:07:02');
INSERT INTO `interviews` VALUES (47, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"ASP\", \"C语言\", \"网络综合布线\", \"汇编语言与接口技术\", \"CAD\", \"网络安全\", \"Java\", \"DELPHI\", \"Pro/Engineer (proe)\", \"Dreamweaver\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"网络工程师\", \"软件开发工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"ASP\", \"C语言\", \"网络综合布线\", \"汇编语言与接口技术\", \"CAD\", \"网络安全\", \"Java\", \"DELPHI\", \"Pro/Engineer (proe)\", \"Dreamweaver\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 16:15:40', '2025-07-22 16:15:40');
INSERT INTO `interviews` VALUES (48, 1, NULL, 'technical', 'technical', 'machine_learning_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"网络安全\", \"C语言\", \"网络综合布线\", \"Pro/Engineer (proe)\", \"DELPHI\", \"Dreamweaver\", \"ASP\", \"CAD\", \"Java\", \"汇编语言与接口技术\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"网络工程师\", \"软件开发工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"网络安全\", \"C语言\", \"网络综合布线\", \"Pro/Engineer (proe)\", \"DELPHI\", \"Dreamweaver\", \"ASP\", \"CAD\", \"Java\", \"汇编语言与接口技术\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 16:45:48', '2025-07-22 16:45:48');
INSERT INTO `interviews` VALUES (49, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"ASP\", \"C语言\", \"Java\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\", \"Dreamweaver\", \"DELPHI\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"技术支持工程师\", \"网络工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"ASP\", \"C语言\", \"Java\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\", \"Dreamweaver\", \"DELPHI\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 17:39:45', '2025-07-22 17:39:45');
INSERT INTO `interviews` VALUES (50, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Pro/Engineer (proe)\", \"ASP\", \"DELPHI\", \"CAD\", \"C语言\", \"网络安全\", \"汇编语言与接口技术\", \"Dreamweaver\", \"网络综合布线\", \"Java\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"技术支持工程师\", \"测试工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Pro/Engineer (proe)\", \"ASP\", \"DELPHI\", \"CAD\", \"C语言\", \"网络安全\", \"汇编语言与接口技术\", \"Dreamweaver\", \"网络综合布线\", \"Java\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 17:46:03', '2025-07-22 17:46:03');
INSERT INTO `interviews` VALUES (51, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"Dreamweaver\", \"DELPHI\", \"CAD\", \"ASP\", \"Java\", \"C语言\", \"网络安全\", \"网络综合布线\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"软件开发工程师\", \"网络工程师\", \"技术支持工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Pro/Engineer (proe)\", \"汇编语言与接口技术\", \"Dreamweaver\", \"DELPHI\", \"CAD\", \"ASP\", \"Java\", \"C语言\", \"网络安全\", \"网络综合布线\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 17:55:49', '2025-07-22 17:55:49');
INSERT INTO `interviews` VALUES (52, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"网络综合布线\", \"CAD\", \"网络安全\", \"Java\", \"DELPHI\", \"C语言\", \"汇编语言与接口技术\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"ASP\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"技术支持工程师\", \"软件开发工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"网络综合布线\", \"CAD\", \"网络安全\", \"Java\", \"DELPHI\", \"C语言\", \"汇编语言与接口技术\", \"Dreamweaver\", \"Pro/Engineer (proe)\", \"ASP\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 18:16:38', '2025-07-22 18:16:38');
INSERT INTO `interviews` VALUES (53, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Dreamweaver\", \"DELPHI\", \"汇编语言与接口技术\", \"Java\", \"网络综合布线\", \"ASP\", \"Pro/Engineer (proe)\", \"网络安全\", \"CAD\", \"C语言\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"软件开发工程师\", \"技术支持工程师\", \"测试工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Dreamweaver\", \"DELPHI\", \"汇编语言与接口技术\", \"Java\", \"网络综合布线\", \"ASP\", \"Pro/Engineer (proe)\", \"网络安全\", \"CAD\", \"C语言\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 20:00:31', '2025-07-22 20:00:31');
INSERT INTO `interviews` VALUES (54, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"ASP\", \"DELPHI\", \"C语言\", \"网络综合布线\", \"Dreamweaver\", \"网络安全\", \"Pro/Engineer (proe)\", \"Java\", \"汇编语言与接口技术\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"测试工程师\", \"技术支持工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"ASP\", \"DELPHI\", \"C语言\", \"网络综合布线\", \"Dreamweaver\", \"网络安全\", \"Pro/Engineer (proe)\", \"Java\", \"汇编语言与接口技术\", \"CAD\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 20:15:40', '2025-07-22 20:15:40');
INSERT INTO `interviews` VALUES (55, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"DELPHI\", \"Pro/Engineer (proe)\", \"ASP\", \"网络安全\", \"CAD\", \"C语言\", \"Java\", \"网络综合布线\", \"Dreamweaver\", \"汇编语言与接口技术\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"技术支持工程师\", \"测试工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"DELPHI\", \"Pro/Engineer (proe)\", \"ASP\", \"网络安全\", \"CAD\", \"C语言\", \"Java\", \"网络综合布线\", \"Dreamweaver\", \"汇编语言与接口技术\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 20:19:02', '2025-07-22 20:19:02');
INSERT INTO `interviews` VALUES (56, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"DELPHI\", \"Pro/Engineer (proe)\", \"ASP\", \"网络安全\", \"CAD\", \"C语言\", \"Java\", \"网络综合布线\", \"Dreamweaver\", \"汇编语言与接口技术\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"技术支持工程师\", \"测试工程师\", \"软件开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"DELPHI\", \"Pro/Engineer (proe)\", \"ASP\", \"网络安全\", \"CAD\", \"C语言\", \"Java\", \"网络综合布线\", \"Dreamweaver\", \"汇编语言与接口技术\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 20:21:38', '2025-07-22 20:21:38');
INSERT INTO `interviews` VALUES (57, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Dreamweaver\", \"C语言\", \"CAD\", \"DELPHI\", \"网络综合布线\", \"汇编语言与接口技术\", \"Java\", \"ASP\", \"网络安全\", \"Pro/Engineer (proe)\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"技术支持工程师\", \"软件开发工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Dreamweaver\", \"C语言\", \"CAD\", \"DELPHI\", \"网络综合布线\", \"汇编语言与接口技术\", \"Java\", \"ASP\", \"网络安全\", \"Pro/Engineer (proe)\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 20:27:27', '2025-07-22 20:27:27');
INSERT INTO `interviews` VALUES (58, 1, NULL, 'technical', 'technical', 'ai_algorithm_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Dreamweaver\", \"C语言\", \"CAD\", \"DELPHI\", \"网络综合布线\", \"汇编语言与接口技术\", \"Java\", \"ASP\", \"网络安全\", \"Pro/Engineer (proe)\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"测试工程师\", \"技术支持工程师\", \"软件开发工程师\", \"网络工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Dreamweaver\", \"C语言\", \"CAD\", \"DELPHI\", \"网络综合布线\", \"汇编语言与接口技术\", \"Java\", \"ASP\", \"网络安全\", \"Pro/Engineer (proe)\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 20:30:41', '2025-07-22 20:30:41');
INSERT INTO `interviews` VALUES (59, 12, 18, 'technical', 'technical', 'big_data_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [18], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"Dreamweaver\", \"C语言\", \"CAD\", \"ProE\", \"DELPHI\", \"网络综合布线\", \"汇编语言与接口技术\", \"WPS\", \"Office\", \"初级工程师证\", \"计算机三级\", \"Java\", \"ASP\", \"网络安全\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，负责研发项目采购跟踪及技术文件编制\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"组织商业赞助活动，负责对外交流联络，提升学生会社会知名度\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"Java\", \"DELPHI\", \"ASP\", \"C语言\", \"汇编语言与接口技术\", \"网络安全\", \"网络综合布线\", \"Dreamweaver\", \"ProE\", \"CAD\", \"WPS\", \"Office\", \"计算机三级\", \"初级工程师证\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，负责研发项目采购跟踪及技术文件编制\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"组织商业赞助活动，负责对外交流联络，提升学生会社会知名度\", \"technologies\": []}], \"positions\": [\"Java开发工程师\", \"软件工程师\", \"技术支持工程师\"], \"strengths\": \"掌握Java、DELPHI、ASP等开发技术，熟悉网络综合布线及安全技术；具备产品工程实习经验，熟悉研发流程管理；拥有初级工程师资质，逻辑思维能力强，擅长团队协作与技术学习。\", \"experience_years\": 0.5}, \"ai_positions\": [\"软件工程师\", \"技术支持工程师\", \"Java开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"Dreamweaver\", \"C语言\", \"CAD\", \"ProE\", \"DELPHI\", \"网络综合布线\", \"汇编语言与接口技术\", \"WPS\", \"Office\", \"初级工程师证\", \"计算机三级\", \"Java\", \"ASP\", \"网络安全\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 20:59:53', '2025-07-22 20:59:53');
INSERT INTO `interviews` VALUES (60, 1, NULL, 'technical', 'technical', 'big_data_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [17], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"C语言\", \"ASP\", \"DELPHI\", \"CAD\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"网络安全\", \"网络综合布线\", \"Java\", \"Dreamweaver\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"C语言\", \"Java\", \"DELPHI\", \"ASP\", \"Dreamweaver\", \"网络安全\", \"网络综合布线\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"CAD\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，编制研发技术文件，负责采购跟踪及库存管理，提供跨部门技术支持。\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"统筹学生会对外交流联络工作，组织商业赞助活动筹集经费，提升学校社会知名度，开展服务同学的各项活动。\", \"technologies\": []}], \"positions\": [\"软件开发工程师\", \"测试工程师\", \"技术支持工程师\", \"网络工程师\"], \"strengths\": \"掌握C语言、Java、DELPHI等多编程语言及网络安全技术，具备产品开发全流程实践经验；熟悉Pro/E、CAD等工程软件，拥有跨部门协作和技术文档编制能力；兼具技术研发与组织协调的双重优势。\", \"experience_years\": 1}, \"ai_positions\": [\"网络工程师\", \"技术支持工程师\", \"软件开发工程师\", \"测试工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"C语言\", \"ASP\", \"DELPHI\", \"CAD\", \"汇编语言与接口技术\", \"Pro/Engineer (proe)\", \"网络安全\", \"网络综合布线\", \"Java\", \"Dreamweaver\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 21:06:30', '2025-07-22 21:06:30');
INSERT INTO `interviews` VALUES (61, 12, 18, 'technical', 'technical', 'big_data_engineer', '测试公司', 'primary', 5, NULL, 'scheduled', NULL, NULL, NULL, NULL, 'friendly', 'frequent', 1.0, 1, '[\"nod\"]', 'video', 1, 1, '[]', 0, 0, NULL, NULL, NULL, NULL, '{\"resume_ids\": [18], \"resume_count\": 1, \"integrated_resume_data\": {\"skills\": [\"C语言\", \"ProE\", \"计算机三级\", \"ASP\", \"DELPHI\", \"CAD\", \"WPS\", \"汇编语言与接口技术\", \"网络安全\", \"网络综合布线\", \"Java\", \"初级工程师证\", \"Office\", \"Dreamweaver\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，负责研发项目采购跟踪及技术文件编制\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"组织商业赞助活动，负责对外交流联络，提升学生会社会知名度\", \"technologies\": []}], \"education\": [], \"ai_analysis\": {\"skills\": [\"Java\", \"DELPHI\", \"ASP\", \"C语言\", \"汇编语言与接口技术\", \"网络安全\", \"网络综合布线\", \"Dreamweaver\", \"ProE\", \"CAD\", \"WPS\", \"Office\", \"计算机三级\", \"初级工程师证\"], \"projects\": [{\"title\": \"产品工程实习生\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，负责研发项目采购跟踪及技术文件编制\", \"technologies\": []}, {\"title\": \"学生会外联部部长\", \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\", \"description\": \"组织商业赞助活动，负责对外交流联络，提升学生会社会知名度\", \"technologies\": []}], \"positions\": [\"Java开发工程师\", \"软件工程师\", \"技术支持工程师\"], \"strengths\": \"掌握Java、DELPHI、ASP等开发技术，熟悉网络综合布线及安全技术；具备产品工程实习经验，熟悉研发流程管理；拥有初级工程师资质，逻辑思维能力强，擅长团队协作与技术学习。\", \"experience_years\": 0.5}, \"ai_positions\": [\"技术支持工程师\", \"软件工程师\", \"Java开发工程师\"], \"personal_info\": {\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}, \"ai_skills_tags\": [\"C语言\", \"ProE\", \"计算机三级\", \"ASP\", \"DELPHI\", \"CAD\", \"WPS\", \"汇编语言与接口技术\", \"网络安全\", \"网络综合布线\", \"Java\", \"初级工程师证\", \"Office\", \"Dreamweaver\"], \"certifications\": [], \"work_experience\": []}}', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 21:10:55', '2025-07-22 21:10:55');

-- ----------------------------
-- Table structure for resumes
-- ----------------------------
DROP TABLE IF EXISTS `resumes`;
CREATE TABLE `resumes`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '简历ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '简历名称',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '简历标题',
  `original_filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `file_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件类型',
  `file_size` int NULL DEFAULT NULL COMMENT '文件大小(字节)',
  `file_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件MD5哈希',
  `raw_text` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '原始文本内容',
  `processed_text` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '处理后文本',
  `status` enum('uploading','processing','completed','error') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'uploading' COMMENT '处理状态',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `view_count` int NULL DEFAULT 0 COMMENT '查看次数',
  `download_count` int NULL DEFAULT 0 COMMENT '下载次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `personal_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '个人信息(JSON格式)',
  `education` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '教育经历(JSON格式)',
  `work_experience` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '工作经历(JSON格式)',
  `skills` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '技能清单(JSON格式)',
  `projects` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '项目经验(JSON格式)',
  `certifications` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '证书资质(JSON格式)',
  `ai_skills_tags` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'AI技能标签(JSON格式)',
  `ai_positions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'AI推荐岗位(JSON格式)',
  `ai_analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'AI综合分析(JSON格式)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_name`(`name`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `resumes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '简历信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of resumes
-- ----------------------------
INSERT INTO `resumes` VALUES (18, 12, '22JIANLI-00395应届生计算机应用专业求职简历', NULL, '22JIANLI-00395应届生计算机应用专业求职简历.pdf', 'C:\\Users\\<USER>\\Desktop\\软件杯后端\\uploads\\12\\1753217715109_22JIANLI-00395.pdf', 'pdf', 209773, NULL, '--- 第1页 ---\n刘小易\n求职意向：计算机应用技术应届生\n出生年月：2000.6.3 手机号码：15300000000\n现居地：广东 邮箱：<EMAIL>\n教育背景\n2017.09—2021.06 xxx大学 计算机应用技术（本科）\n主修课程：计算机网络、数据结构、操作系统、C语言、Dreamweaver、JAVA、DELPHI、ASP、汇编语\n言与接口技术、网络安全、网络综合布线、网页设计和网站建设等。\n实践经历\n20xx.xx—20xx.xx xxx有限公司 产品工程（实习生）\n1. 协助工程师完成产品设计和开发工作，以及样品的加工制作；\n2. 配合工程师完成设备测试调试工作，完成研发相关技术文件的编制和归档；\n3. 负责研发项目采购跟踪，库存整理，工具保管；\n4. 对其他部门的技术支持和沟通，完成领导交办其它工作内容。\n20xx.xx—20xx.xx 学校学生会（外联部） 部长\n1. 在主席团的领导下开展各类活动，全面负责学校学生会的对外交流联络工作；\n2. 组织各类商业赞助活动，为学生会活动提供经费；\n3. 向外展示学校学生的真我风采,努力提高我校学生会在社会上的知名度；\n4. 积极开展各项活动服务于同学,用行动来阐释外联部的宗旨等工作。\n个人技能\n专业技能：英语CET-4，普通话二甲等证书，计算机三级证书，初级工程师证\n其他技能：熟悉运用proe、cad，以及wps，office等办公软件\n自我评价\n个人方面：性格乐观开朗，为人陈恳，乐于助人，待人真诚，善解人意，扎实基础，有较强的逻辑思维能\n力，善于分析，归纳，解决问题，对技术有浓厚兴趣，喜欢并不断学习新技术。\n工作态度：工作中认真负责，不以自我为中心，具有团队合作精神，有自制力，做事情坚持有始有终，从\n不半途而废，喜欢与人相交，并虚心向他人学习,会用100%的热情和精力投入到工作中。', NULL, 'completed', NULL, 0, 0, '2025-07-22 20:55:15', '2025-07-22 20:55:43', '{\"email\": \"<EMAIL>\", \"phone\": \"15300000000\"}', NULL, NULL, '[\"Java\", \"DELPHI\", \"ASP\", \"C语言\", \"汇编语言与接口技术\", \"网络安全\", \"网络综合布线\", \"Dreamweaver\", \"ProE\", \"CAD\", \"WPS\", \"Office\", \"计算机三级\", \"初级工程师证\"]', '[{\"title\": \"产品工程实习生\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，负责研发项目采购跟踪及技术文件编制\", \"technologies\": [], \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\"}, {\"title\": \"学生会外联部部长\", \"description\": \"组织商业赞助活动，负责对外交流联络，提升学生会社会知名度\", \"technologies\": [], \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\"}]', NULL, '[\"Java\", \"DELPHI\", \"ASP\", \"C语言\", \"汇编语言与接口技术\", \"网络安全\", \"网络综合布线\", \"Dreamweaver\", \"ProE\", \"CAD\", \"WPS\", \"Office\", \"计算机三级\", \"初级工程师证\"]', '[\"Java开发工程师\", \"软件工程师\", \"技术支持工程师\"]', '{\"skills\": [\"Java\", \"DELPHI\", \"ASP\", \"C语言\", \"汇编语言与接口技术\", \"网络安全\", \"网络综合布线\", \"Dreamweaver\", \"ProE\", \"CAD\", \"WPS\", \"Office\", \"计算机三级\", \"初级工程师证\"], \"projects\": [{\"title\": \"产品工程实习生\", \"description\": \"协助工程师完成产品设计和开发工作，参与设备测试调试，负责研发项目采购跟踪及技术文件编制\", \"technologies\": [], \"period\": \"20xx.xx—20xx.xx\", \"company\": \"xxx有限公司\"}, {\"title\": \"学生会外联部部长\", \"description\": \"组织商业赞助活动，负责对外交流联络，提升学生会社会知名度\", \"technologies\": [], \"period\": \"20xx.xx—20xx.xx\", \"company\": \"学校学生会\"}], \"positions\": [\"Java开发工程师\", \"软件工程师\", \"技术支持工程师\"], \"experience_years\": 0.5, \"strengths\": \"掌握Java、DELPHI、ASP等开发技术，熟悉网络综合布线及安全技术；具备产品工程实习经验，熟悉研发流程管理；拥有初级工程师资质，逻辑思维能力强，擅长团队协作与技术学习。\"}');

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '配置值',
  `config_type` enum('string','integer','float','boolean','json') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'string' COMMENT '配置类型',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '配置描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `config_key`(`config_key`) USING BTREE,
  INDEX `idx_config_key`(`config_key`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of system_config
-- ----------------------------
INSERT INTO `system_config` VALUES (1, 'app_name', '软件杯面试系统', 'string', '应用名称', '2025-07-15 22:32:01', '2025-07-15 22:32:01');
INSERT INTO `system_config` VALUES (2, 'app_version', '1.0.0', 'string', '应用版本', '2025-07-15 22:32:01', '2025-07-15 22:32:01');
INSERT INTO `system_config` VALUES (3, 'max_resume_size', '10485760', 'integer', '最大简历文件大小(字节)', '2025-07-15 22:32:01', '2025-07-15 22:32:01');
INSERT INTO `system_config` VALUES (4, 'max_interview_duration', '3600', 'integer', '最大面试时长(秒)', '2025-07-15 22:32:01', '2025-07-15 22:32:01');
INSERT INTO `system_config` VALUES (5, 'ai_analysis_enabled', 'true', 'boolean', '是否启用AI分析', '2025-07-15 22:32:01', '2025-07-15 22:32:01');
INSERT INTO `system_config` VALUES (6, 'default_question_count', '5', 'integer', '默认问题数量', '2025-07-15 22:32:01', '2025-07-15 22:32:01');

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `gender` enum('male','female','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '性别',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期',
  `contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系方式(微信/QQ)',
  `education_info` json NULL COMMENT '教育背景信息',
  `skills` json NULL COMMENT '技能信息',
  `job_preferences` json NULL COMMENT '求职偏好设置',
  `interview_preferences` json NULL COMMENT '面试偏好设置',
  `privacy_settings` json NULL COMMENT '隐私设置',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否激活',
  `is_verified` tinyint(1) NULL DEFAULT 0 COMMENT '是否已验证',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `delete_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '删除原因',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `target_field` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'ai',
  `target_positions` json NULL,
  `target_companies` json NULL,
  `work_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `company_size` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `work_experience` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE,
  UNIQUE INDEX `email`(`email`) USING BTREE,
  INDEX `idx_username`(`username`) USING BTREE,
  INDEX `idx_email`(`email`) USING BTREE,
  INDEX `idx_phone`(`phone`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户基础信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, '123123', '<EMAIL>', '123123', '', '', '123123', 'male', '2005-02-02', '12313231213', '{\"gpa\": \"3.6\", \"major\": \"计算机科学与技术\", \"degree\": \"bachelor\", \"school\": \"天津大学\", \"college\": \"计算机学院\", \"courses\": \"Java开发，C语言，计算机组成原理，操作系统\", \"end_date\": \"2027-09-15\", \"start_date\": \"2023-09-15\", \"description\": \"\"}', NULL, NULL, '{\"metadata\": {\"version\": \"1.0.0\", \"created_at\": \"2025-07-22T20:43:55.446Z\", \"updated_at\": \"2025-07-22T20:43:55.446Z\"}, \"noise_config\": {\"noise_volume\": 0.3, \"enabled_noises\": [], \"noise_frequency\": 0.2, \"custom_noise_url\": \"\"}, \"voice_config\": {\"voice_pitch\": 1, \"voice_speed\": \"0.5\", \"voice_style\": \"professional\", \"voice_volume\": 0.8}, \"avatar_config\": {\"avatar_style\": 1, \"avatar_gender\": \"neutral\", \"avatar_age_range\": \"middle\"}, \"advanced_config\": {\"ai_model\": \"spark\", \"context_memory\": 5, \"response_delay\": 1000, \"difficulty_adaptation\": true}, \"recording_config\": {\"ai_highlight\": true, \"auto_summary\": true, \"auto_recording\": true, \"recording_type\": \"audio\", \"recording_quality\": \"medium\", \"improvement_marking\": true}, \"expression_config\": {\"enabled_expressions\": [\"nod\", \"frown\"], \"expression_frequency\": 0.5, \"expression_intensity\": 0.7}, \"interaction_config\": {\"interaction_mode\": \"frequent\", \"question_frequency\": 3, \"follow_up_probability\": 0.3}}', NULL, 1, 1, NULL, NULL, '2025-07-15 22:32:01', '2025-07-22 21:11:46', '2025-07-22 21:11:46', 'big_data', '[\"big_data_engineer\"]', '[\"字节跳动\", \"阿里巴巴\"]', '北京', 'startup', '0-1');
INSERT INTO `users` VALUES (2, '123456', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LeGNhCnOhGfyEVUwS', NULL, NULL, '测试用户', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, '2025-07-15 22:32:01', '2025-07-20 22:14:40', NULL, 'ai', '[]', '[]', '', '', '');
INSERT INTO `users` VALUES (3, 'Jia', '<EMAIL>', '123123', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 0, NULL, NULL, '2025-07-16 08:27:13', '2025-07-20 22:14:40', NULL, 'ai', '[]', '[]', '', '', '');
INSERT INTO `users` VALUES (4, '32332', '<EMAIL>', '123123', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 0, NULL, NULL, '2025-07-16 08:30:08', '2025-07-20 22:14:40', '2025-07-16 08:30:31', 'ai', '[]', '[]', '', '', '');
INSERT INTO `users` VALUES (5, '11111', '<EMAIL>', '123123', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 0, NULL, NULL, '2025-07-16 08:34:33', '2025-07-20 22:14:40', '2025-07-16 08:34:43', 'ai', '[]', '[]', '', '', '');
INSERT INTO `users` VALUES (6, '423234', '<EMAIL>', '123123', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 0, NULL, NULL, '2025-07-16 13:37:16', '2025-07-20 22:14:40', NULL, 'ai', '[]', '[]', '', '', '');
INSERT INTO `users` VALUES (11, '123123123', '<EMAIL>', '123123', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 0, NULL, NULL, '2025-07-22 12:16:34', '2025-07-22 12:16:37', '2025-07-22 12:16:37', 'ai', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `users` VALUES (12, '哈哈123', '<EMAIL>', '123123', '15811111111', NULL, '哈哈123', 'male', '2009-07-02', '123123123123', '{\"gpa\": \"3.7\", \"major\": \"计算机科学与技术\", \"degree\": \"bachelor\", \"school\": \"天津大学\", \"college\": \"计算机学院\", \"courses\": \"C语言，操作系统\", \"end_date\": \"2029-07-03\", \"start_date\": \"2025-07-03\", \"description\": \"\"}', NULL, NULL, NULL, NULL, 1, 0, NULL, NULL, '2025-07-22 20:51:26', '2025-07-22 21:10:44', '2025-07-22 21:10:44', 'big_data', '[\"big_data_engineer\"]', '[\"科大讯飞\"]', '北京', 'large', '0-1');

SET FOREIGN_KEY_CHECKS = 1;
