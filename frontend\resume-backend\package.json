{"name": "resume-management-backend", "version": "1.0.0", "description": "简历管理后端服务，集成讯飞文档识别API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["resume", "xun<PERSON>i", "ocr", "document-recognition", "file-upload"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "axios": "^1.6.0", "form-data": "^4.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}