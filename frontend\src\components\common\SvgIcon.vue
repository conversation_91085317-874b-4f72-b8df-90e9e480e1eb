<template>
  <!-- 登录背景图标 - 用于登录页面背景 -->
  <svg v-if="name === 'login-background'" class="svg-icon" viewBox="0 0 1920 1080">
    <defs>
      <linearGradient id="loginGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
      </linearGradient>
      <radialGradient id="circleGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.1" />
        <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.02" />
      </radialGradient>
    </defs>
    
    <!-- 背景基础 -->
    <rect width="1920" height="1080" fill="url(#loginGradient)"/>
    
    <!-- 装饰圆形 -->
    <circle cx="300" cy="200" r="120" fill="url(#circleGradient)" opacity="0.6"/>
    <circle cx="1600" cy="300" r="80" fill="url(#circleGradient)" opacity="0.4"/>
    <circle cx="200" cy="800" r="60" fill="url(#circleGradient)" opacity="0.5"/>
    <circle cx="1700" cy="900" r="100" fill="url(#circleGradient)" opacity="0.3"/>
    <circle cx="800" cy="100" r="40" fill="url(#circleGradient)" opacity="0.7"/>
    <circle cx="1200" cy="1000" r="70" fill="url(#circleGradient)" opacity="0.4"/>
    
    <!-- 流动线条 -->
    <path d="M0,400 Q400,300 800,400 T1600,400 Q1800,450 1920,400 L1920,460 Q1800,510 1600,460 T800,460 Q400,360 0,460 Z" 
          fill="rgba(255,255,255,0.05)" opacity="0.8"/>
    <path d="M0,600 Q300,500 600,600 T1200,600 Q1500,650 1920,600 L1920,660 Q1500,710 1200,660 T600,660 Q300,560 0,660 Z" 
          fill="rgba(255,255,255,0.03)" opacity="0.6"/>
    
    <!-- 点状装饰 -->
    <circle cx="100" cy="300" r="3" fill="rgba(255,255,255,0.4)"/>
    <circle cx="500" cy="250" r="2" fill="rgba(255,255,255,0.3)"/>
    <circle cx="900" cy="400" r="4" fill="rgba(255,255,255,0.2)"/>
    <circle cx="1300" cy="200" r="2" fill="rgba(255,255,255,0.5)"/>
    <circle cx="1700" cy="500" r="3" fill="rgba(255,255,255,0.3)"/>
    <circle cx="400" cy="700" r="2" fill="rgba(255,255,255,0.4)"/>
    <circle cx="1100" cy="800" r="3" fill="rgba(255,255,255,0.2)"/>
    <circle cx="1500" cy="750" r="2" fill="rgba(255,255,255,0.3)"/>
  </svg>
  
  

   <!-- 框架图标 - 用于PersonalInfo.vue头像占位符，来源于原始FRAME.svg文件 -->
   <svg v-else-if="name === 'frame'" class="svg-icon" viewBox="0 0 48 48">
    <path d="M35.625 27L28.5 27C27.6716 27 27 27.6716 27 28.5C27 29.3284 27.6716 30 28.5 30L35.625 30Q37.7947 30 39.6964 29.4291Q41.6814 28.8332 43.1672 27.6773Q44.7546 26.4422 45.6078 24.6971Q46.5 22.8722 46.5 20.6625Q46.5 18.4492 45.4739 16.59Q44.557 14.9286 42.9062 13.7023Q41.447 12.6184 39.5832 11.9944Q38.4644 11.6198 37.3258 11.4506Q36.9045 8.9585 35.8492 6.79846Q34.5822 4.2051 32.5163 2.31532Q30.4905 0.462188 27.9132 -0.519283Q25.3379 -1.5 22.5 -1.5Q19.9074 -1.5 17.5863 -0.676364Q15.4382 0.0859058 13.6531 1.50406Q12.0017 2.81593 10.8024 4.57219Q9.95128 5.81868 9.40355 7.17449Q7.58117 7.45223 5.92629 8.11827Q3.76856 8.98668 2.14868 10.4147Q0.422268 11.9367 -0.509762 13.9347Q-1.5 16.0575 -1.5 18.525Q-1.5 21.0126 -0.468701 23.1968Q0.517385 25.2851 2.32423 26.8457Q4.08164 28.3635 6.38014 29.1793Q8.69273 30 11.25 30L16.5 30C17.3284 30 18 29.3284 18 28.5C18 27.6716 17.3284 27 16.5 27L11.25 27Q9.2093 27 7.38353 26.352Q5.61608 25.7248 4.28514 24.5753Q2.95898 23.4299 2.24409 21.9158Q1.5 20.34 1.5 18.525Q1.5 16.7228 2.20898 15.203Q2.87614 13.7728 4.13257 12.6651Q5.36625 11.5775 7.04637 10.9013Q8.72616 10.2253 10.6419 10.0433Q10.7509 10.0329 10.8572 10.0069Q10.9635 9.98078 11.065 9.93954Q11.1664 9.8983 11.2607 9.84277Q11.3551 9.78725 11.4404 9.7186Q11.5257 9.64996 11.6001 9.56966Q11.6746 9.48936 11.7365 9.3991Q11.7985 9.30885 11.8467 9.21054Q11.8949 9.11224 11.9284 9.00799Q12.3906 7.56628 13.2799 6.26394Q14.2232 4.88255 15.5191 3.85305Q16.9127 2.74594 18.5896 2.15091Q20.4239 1.5 22.5 1.5Q24.786 1.5 26.8455 2.2843Q28.8879 3.06208 30.4914 4.52889Q32.1368 6.03404 33.1537 8.11535Q34.2298 10.3181 34.5081 12.9809Q34.522 13.1134 34.5589 13.2414Q34.5959 13.3694 34.6549 13.4888Q34.7138 13.6083 34.7929 13.7155Q34.872 13.8227 34.9687 13.9142Q35.0654 14.0058 35.1768 14.0789Q35.2882 14.152 35.4106 14.2044Q35.5331 14.2567 35.6629 14.2866Q35.7927 14.3166 35.9258 14.3232Q37.2915 14.3908 38.6307 14.8392Q40.0415 15.3115 41.1173 16.1106Q43.5 17.8806 43.5 20.6625Q43.5 23.6174 41.325 25.3095Q40.2818 26.1211 38.8339 26.5558Q37.3541 27 35.625 27Z" fill="rgb(156, 163, 175)" transform="matrix(1 0 0 1 1.5 5.98031)"/>
    <path d="M7.5 3.62132L10.9393 7.06066L13.0607 4.93934L6 -2.12132L-1.06066 4.93934L1.06066 7.06066L4.5 3.62132L4.5 24.0394L7.5 24.0394L7.5 3.62132Z" fill="rgb(156, 163, 175)" transform="matrix(1 0 0 1 18 17.9803)"/>
  </svg>

  <!-- 云上传图标 - 用于ResumeManagement.vue文件上传区域 -->
  <svg v-else-if="name === 'upload'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M17 8l-5-5-5 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M12 3v12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>

  <!-- 文件图标 - 用于ResumeManagement.vue简历列表中显示文件类型 -->
  <svg v-else-if="name === 'file'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M14 2v6h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>

  <!-- 查看图标（眼睛）- 用于ResumeManagement.vue操作按钮，查看简历详情 -->
  <svg v-else-if="name === 'view'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
  </svg>

  <!-- 编辑图标（笔）- 用于ResumeManagement.vue操作按钮，编辑简历 -->
  <svg v-else-if="name === 'edit'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
  </svg>

  <!-- 删除图标（垃圾桶）- 用于ResumeManagement.vue操作按钮，删除简历 -->
  <svg v-else-if="name === 'delete'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <path d="M3 6h18" stroke="currentColor" stroke-width="2"/>
    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" stroke="currentColor" stroke-width="2"/>
    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2"/>
  </svg>

  <!-- 工作图标 - 用于岗位偏好页面的人工智能领域 -->
  <svg v-else-if="name === 'work'" class="svg-icon" viewBox="0 0 40 25.6">
    <path d="M34 8.8L34 22.4Q33.9984 23.7249 32.8273 24.6618C32.0465 25.2864 31.1041 25.5991 30 25.6L10 25.6C8.89588 25.5991 7.95346 25.2864 7.17273 24.6618Q6.00164 23.7249 6 22.4L6 8.8Q5.99933 7.14292 7.46399 5.97119Q8.92866 4.79946 11 4.8L18 4.8L18 1.6Q18 0.937258 18.5858 0.468629Q19.1716 0 20 0Q20.8284 0 21.4142 0.468629Q22 0.937258 22 1.6L22 4.8L29 4.8C30.3809 4.79964 31.5596 5.19004 32.536 5.97119C33.5125 6.75235 34.0005 7.69528 34 8.8ZM15.7678 11.3858Q16.5 11.9716 16.5 12.8C16.5001 13.3523 16.256 13.8237 15.7678 14.2143Q15.0356 14.8001 14 14.8Q12.9645 14.8 12.2322 14.2142Q11.5 13.6284 11.5 12.8Q11.5 11.9716 12.2322 11.3858Q12.9645 10.8 14 10.8Q15.0355 10.8 15.7678 11.3858ZM27.7678 11.3858Q28.5 11.9716 28.5 12.8C28.5001 13.3523 28.256 13.8237 27.7678 14.2143Q27.0356 14.8001 26 14.8Q24.9645 14.8 24.2322 14.2142Q23.5 13.6284 23.5 12.8Q23.5 11.9716 24.2322 11.3858Q24.9645 10.8 26 10.8Q27.0355 10.8 27.7678 11.3858ZM2 11.2L4 11.2L4 20.8L2 20.8C1.44744 20.8005 0.975803 20.6445 0.585085 20.3319C0.194366 20.0194 -0.0006615 19.642 2e-06 19.2L2e-06 12.8Q-0.000992969 12.1369 0.585085 11.6681Q1.17116 11.1992 2 11.2ZM40 12.8L40 19.2C40.0007 19.642 39.8056 20.0194 39.4149 20.3319Q38.8288 20.8008 38 20.8L36 20.8L36 11.2L38 11.2Q38.8288 11.1992 39.4149 11.6681C39.8056 11.9806 40.0007 12.358 40 12.8ZM12 20.8L12 19.2L16 19.2L16 20.8L12 20.8ZM18 20.8L18 19.2L22 19.2L22 20.8L18 20.8ZM24 20.8L24 19.2L28 19.2L28 20.8L24 20.8Z" 
          fill-rule="evenodd" 
          transform="matrix(1 0 0 1 0 4.88758e-05)" 
          fill="rgb(114, 45, 209)"/>
  </svg>

  <!-- 数据库图标 - 用于岗位偏好页面的大数据领域 -->
  <svg v-else-if="name === 'database'" class="svg-icon" viewBox="0 0 35 40">
    <path d="M35 5.7143L35 9.2857C35 12.433 27.1615 15 17.5 15C7.83852 15 0 12.433 0 9.2857L0 5.7143C0 2.56695 7.83852 0 17.5 0C27.1615 0 35 2.56695 35 5.7143ZM35 21.7857L35 13.75C31.2402 16.3393 24.3587 17.5447 17.5 17.5447C10.6412 17.5447 3.75977 16.3393 0 13.75L0 21.7857C0 24.933 7.83852 27.5 17.5 27.5C27.1615 27.5 35 24.933 35 21.7857ZM35 26.25L35 34.2857C35 37.433 27.1615 40 17.5 40C7.83852 40 0 37.433 0 34.2857L0 26.25C3.75977 28.8393 10.6412 30.0447 17.5 30.0447C24.3587 30.0447 31.2402 28.8393 35 26.25Z" 
          fill="rgb(114, 45, 209)" 
          transform="matrix(1 0 0 1 0 0)"/>
  </svg>

  <!-- 网络图标 - 用于岗位偏好页面的物联网领域 -->
  <svg v-else-if="name === 'network'" class="svg-icon" viewBox="0 0 40 25.6">
    <path d="M40 13.2L40 12.4C40 11.958 39.5525 11.6 39 11.6L21.5 11.6L21.5 9.6L26 9.6C27.1044 9.6 28 8.8835 28 8L28 1.6C28 0.7165 27.1044 0 26 0L14 0C12.8956 0 12 0.7165 12 1.6L12 8C12 8.8835 12.8956 9.6 14 9.6L18.5 9.6L18.5 11.6L1 11.6C0.4475 11.6 0 11.958 0 12.4L0 13.2C0 13.642 0.4475 14 1 14L7.5 14L7.5 16L4 16C2.89563 16 2 16.7165 2 17.6L2 24C2 24.8835 2.89563 25.6 4 25.6L14 25.6C15.1044 25.6 16 24.8835 16 24L16 17.6C16 16.7165 15.1044 16 14 16L10.5 16L10.5 14L29.5 14L29.5 16L26 16C24.8956 16 24 16.7165 24 17.6L24 24C24 24.8835 24.8956 25.6 26 25.6L36 25.6C37.1044 25.6 38 24.8835 38 24L38 17.6C38 16.7165 37.1044 16 36 16L32.5 16L32.5 14L39 14C39.5525 14 40 13.642 40 13.2ZM16 3.2L16 6.4L24 6.4L24 3.2L16 3.2ZM6 22.4L12 22.4L12 19.2L6 19.2L6 22.4ZM28 22.4L34 22.4L34 19.2L28 19.2L28 22.4Z" 
          fill-rule="evenodd" 
          transform="matrix(1 0 0 1 0 4.88758e-05)" 
          fill="rgb(114, 45, 209)"/>
  </svg>


  <!--账号设置图标 -->
  <!-- 手机图标 - 用于手机绑定 -->
  <svg v-else-if="name === 'container1'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <rect x="5" y="2" width="14" height="20" rx="2" ry="2" stroke="rgb(114, 45, 209)" stroke-width="2"/>
    <line x1="12" y1="18" x2="12.01" y2="18" stroke="rgb(114, 45, 209)" stroke-width="2"/>
  </svg>

  <!-- 历史图标 - 用于登录历史 -->
  <svg v-else-if="name === 'container2'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" stroke="rgb(114, 45, 209)" stroke-width="2"/>
    <polyline points="12,6 12,12 16,14" stroke="rgb(114, 45, 209)" stroke-width="2"/>
  </svg>

  <!-- 盾牌图标 - 用于数据共享授权 -->
  <svg v-else-if="name === 'container3'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="rgb(114, 45, 209)" stroke-width="2"/>
  </svg>

  <!-- 眼睛图标 - 用于简历可见范围 -->
  <svg v-else-if="name === 'container4'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="rgb(114, 45, 209)" stroke-width="2"/>
    <circle cx="12" cy="12" r="3" stroke="rgb(114, 45, 209)" stroke-width="2"/>
  </svg>

  <!-- 垃圾桶图标 - 用于痕迹清除 -->
  <svg v-else-if="name === 'container5'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <path d="M3 6h18" stroke="rgb(114, 45, 209)" stroke-width="2"/>
    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" stroke="rgb(114, 45, 209)" stroke-width="2"/>
    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="rgb(114, 45, 209)" stroke-width="2"/>
  </svg>

  <!-- 容器图标 - 黑色矩形占位符图标 -->
  <svg v-else-if="name === 'container'" class="svg-icon" viewBox="0 0 20 20">
    <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
  </svg>

  <!-- 锁图标 - 安全/隐私保护图标 -->
  <svg v-else-if="name === 'lock'" class="svg-icon" viewBox="0 0 13.75 18.75">
    <path d="M11.25 6.875L10.625 6.875L10.625 3.75Q10.625 2.1967 9.52665 1.09835Q8.4283 0 6.875 0Q5.3217 0 4.22335 1.09835Q3.125 2.1967 3.125 3.75L3.125 6.875L2.5 6.875Q1.46493 6.87613 0.733032 7.60803Q0.0011301 8.33994 0 9.375L0 16.25C0.000753398 16.94 0.245097 17.529 0.733032 18.017C1.22097 18.5049 1.80996 18.7492 2.5 18.75L11.25 18.75C11.94 18.7492 12.529 18.5049 13.017 18.017Q13.7489 17.2851 13.75 16.25L13.75 9.375Q13.7489 8.33994 13.017 7.60803C12.529 7.1201 11.94 6.87575 11.25 6.875ZM9.375 3.75L9.375 6.875L4.375 6.875L4.375 3.75Q4.375 2.71447 5.10723 1.98223Q5.83947 1.25 6.875 1.25Q7.91053 1.25 8.64277 1.98223Q9.375 2.71447 9.375 3.75Z" 
          fill-rule="evenodd" 
          transform="matrix(1 0 0 1 0 0)" 
          fill="rgb(114, 45, 209)"/>
  </svg>


 










   <!--面试图标-->
  
  <!-- 语音表达图标 - 麦克风图标 -->
  <svg v-else-if="name === 'container6'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <path d="M12 1a4 4 0 0 0-4 4v6a4 4 0 0 0 8 0V5a4 4 0 0 0-4-4z" stroke="rgb(114, 45, 209)" stroke-width="2"/>
    <path d="M19 10v1a7 7 0 0 1-14 0v-1" stroke="rgb(114, 45, 209)" stroke-width="2"/>
    <line x1="12" y1="19" x2="12" y2="23" stroke="rgb(114, 45, 209)" stroke-width="2"/>
    <line x1="8" y1="23" x2="16" y2="23" stroke="rgb(114, 45, 209)" stroke-width="2"/>
  </svg>

  <!-- 行为表现图标 - 眼睛图标 -->
  <svg v-else-if="name === 'container7'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="rgb(114, 45, 209)" stroke-width="2"/>
    <circle cx="12" cy="12" r="3" stroke="rgb(114, 45, 209)" stroke-width="2"/>
  </svg>

  <!-- 技术内容图标 - 代码图标 -->
  <svg v-else-if="name === 'container8'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <polyline points="16,18 22,12 16,6" stroke="rgb(114, 45, 209)" stroke-width="2"/>
    <polyline points="8,6 2,12 8,18" stroke="rgb(114, 45, 209)" stroke-width="2"/>
  </svg>

  <!-- 压力应对图标 - 图表图标 -->
  <svg v-else-if="name === 'container9'" class="svg-icon" viewBox="0 0 24 24" fill="none">
    <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="rgb(114, 45, 209)" stroke-width="2"/>
  </svg>
  





<!--测评报告页面-->

    <!-- 奖杯图标 -->
    <svg v-else-if="name === 'trophy'" class="svg-icon" viewBox="0 0 20 20">
    <g>
      <path d="M9.99651 8.95034Q10.6266 7.9473 10.6266 6.875L10.6219 0.624388Q10.6219 0.107231 10.2558 -0.258885Q9.88964 -0.625 9.37187 -0.625L0.629352 -0.614843Q0.113455 -0.614846 -0.252425 -0.250109Q-0.618304 0.114628 -0.619919 0.631249L-0.625 6.87449Q-0.625 7.94724 0.0047406 8.95023Q0.540657 9.80379 1.4636 10.5024Q2.29283 11.1301 3.25096 11.5021Q3.8468 11.7335 4.37656 11.8213L4.37656 15.625L1.87656 15.625L1.87656 16.875L8.12656 16.875L8.12656 15.625L5.62656 15.625L5.62656 11.8208Q6.15501 11.7328 6.74923 11.5022Q7.70761 11.1302 8.53709 10.5025Q9.46038 9.80386 9.99651 8.95034ZM9.37187 0.625L9.37656 6.87525Q9.37648 7.5874 8.93801 8.28545Q8.52361 8.94517 7.78283 9.5057Q7.09255 10.028 6.29691 10.3369Q5.55459 10.625 5 10.625Q4.44546 10.625 3.70338 10.3369Q2.90803 10.0281 2.21804 9.50577Q1.47759 8.94528 1.06337 8.28556Q0.625 7.58735 0.625 6.87551L0.630075 0.635157L9.37187 0.625Z" fill-rule="evenodd" transform="matrix(1 0 0 1 4.99844 1.875)" fill="currentColor"/>
      <path d="M3.125 0.625L0.625 0.625Q0.625 1.3399 0.832459 2.04133Q1.03652 2.73128 1.40065 3.27251Q1.76023 3.807 2.20786 4.09313Q2.64882 4.375 3.125 4.375L3.125 5.625Q2.28344 5.625 1.53463 5.14635Q0.868114 4.7203 0.363514 3.97026Q-0.107725 3.26982 -0.366212 2.39585Q-0.625 1.52087 -0.625 0.625L-0.625 -0.625L3.125 -0.625L3.125 0.625ZM13.125 -0.625L16.875 -0.625L16.875 0.625Q16.875 1.52087 16.6162 2.39585Q16.3577 3.26982 15.8865 3.97026Q15.3819 4.7203 14.7154 5.14635Q13.9666 5.625 13.125 5.625L13.125 4.375Q13.6012 4.375 14.0421 4.09313Q14.4898 3.807 14.8494 3.27251Q15.2135 2.73128 15.4175 2.04133Q15.625 1.33989 15.625 0.625L13.125 0.625L13.125 -0.625Z" fill-rule="evenodd" transform="matrix(1 0 0 1 1.875 3.75)" fill="currentColor"/>
    </g>
  </svg>
    
  <!--面试图标-->

  <!-- 技术能力图标 - 01.svg -->
  <svg v-else-if="name === 'technical-ability'" class="svg-icon" viewBox="0 0 20 20">
    <defs>
      <mask id="mask7600938101" style="mask-type:alpha">
        <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
      </mask>
    </defs>
    <g mask="url(#mask7600938101)">
      <defs>
        <mask id="mask5486367083" style="mask-type:alpha">
          <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask5486367083)">
        <path d="M12.4285 0.334296C12.5093 0.486845 12.5244 0.645617 12.4736 0.810612L7.47362 17.0606C7.42283 17.2255 7.32117 17.3484 7.16865 17.4291C7.01612 17.5099 6.85738 17.5249 6.69243 17.4742C6.52747 17.4234 6.4046 17.3218 6.3238 17.1693C6.24301 17.0168 6.22794 16.8581 6.27862 16.6931L11.2786 0.443112Q11.3547 0.19562 11.5836 0.0744347C11.7361 -0.00635525 11.8949 -0.0213802 12.0599 0.029361Q12.3074 0.105473 12.4285 0.334296ZM5.62723 4.37686Q5.62723 4.11752 5.44362 3.93436Q5.26046 3.75075 5.00112 3.75075Q4.74177 3.75075 4.55862 3.93436L0.183618 8.30936Q0 8.49252 0 8.75186Q6.25849e-07 9.01121 0.183618 9.19436L4.55862 13.5694C4.68084 13.6914 4.82831 13.7524 5.00102 13.7523C5.17373 13.7522 5.32115 13.6911 5.44328 13.569C5.5654 13.4469 5.6265 13.2995 5.62657 13.1268C5.62663 12.9541 5.56565 12.8066 5.44362 12.6844L1.50987 8.75186L5.44362 4.81936Q5.62723 4.63621 5.62723 4.37686ZM13.125 4.37686Q13.125 4.11752 13.3086 3.93436Q13.4918 3.75075 13.7511 3.75075Q14.0105 3.75075 14.1936 3.93436L18.5686 8.30936C18.691 8.43147 18.7522 8.57897 18.7522 8.75186C18.7522 8.92476 18.691 9.07226 18.5686 9.19436L14.1936 13.5694C14.0714 13.6914 13.9239 13.7524 13.7512 13.7523C13.5785 13.7522 13.4311 13.6911 13.309 13.569C13.1868 13.4469 13.1257 13.2995 13.1257 13.1268C13.1256 12.9541 13.1866 12.8066 13.3086 12.6844L17.2424 8.75186L13.3086 4.81936Q13.125 4.63621 13.125 4.37686Z" fill-rule="evenodd" transform="matrix(1 0 0 1 0.623883 1.24814)" fill="rgb(114, 45, 209)"/>
      </g>
    </g>
  </svg>

  <!-- 表达逻辑图标 - 2.svg -->
  <svg v-else-if="name === 'expression-logic'" class="svg-icon" viewBox="0 0 20 20">
    <defs>
      <mask id="mask4932482578" style="mask-type:alpha">
        <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
      </mask>
    </defs>
    <g mask="url(#mask4932482578)">
      <defs>
        <mask id="mask4478683454" style="mask-type:alpha">
          <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask4478683454)">
        <path d="M5e-06 7.55656Q-0.0001225 3.52832 3.35814 1.30373Q6.7164 -0.920864 10.4255 0.650435Q14.1347 2.22173 14.873 6.18174Q15.6112 10.1417 12.7175 12.9441C12.4638 13.1891 12.2688 13.4441 12.1513 13.7178L11.1988 15.9291C11.1494 16.0436 11.0726 16.1351 10.9684 16.2037C10.8642 16.2722 10.7497 16.3065 10.625 16.3066C10.7976 16.3066 10.9449 16.3676 11.0669 16.4896C11.189 16.6117 11.25 16.759 11.25 16.9316C11.25 17.1041 11.189 17.2515 11.0669 17.3735C10.9449 17.4955 10.7976 17.5566 10.625 17.5566C10.7976 17.5566 10.9449 17.6176 11.0669 17.7396Q11.25 17.9227 11.25 18.1816C11.25 18.3541 11.189 18.5015 11.0669 18.6235C10.9449 18.7455 10.7976 18.8066 10.625 18.8066L10.345 19.3653C10.2392 19.577 10.0857 19.7451 9.88441 19.8696Q9.58251 20.0564 9.2275 20.0566L5.7725 20.0566C5.53584 20.0564 5.31687 19.9941 5.1156 19.8696C4.91432 19.7451 4.7608 19.577 4.655 19.3653L4.375 18.8066C4.20242 18.8066 4.0551 18.7455 3.93306 18.6235C3.81102 18.5015 3.75001 18.3541 3.75001 18.1816C3.75001 18.009 3.81102 17.8617 3.93306 17.7396C4.0551 17.6176 4.20242 17.5566 4.375 17.5566C4.20242 17.5566 4.0551 17.4955 3.93306 17.3735C3.81102 17.2515 3.75001 17.1041 3.75001 16.9316C3.75001 16.759 3.81102 16.6117 3.93306 16.4896C4.0551 16.3676 4.20242 16.3066 4.375 16.3066C4.25005 16.3068 4.13535 16.2726 4.0309 16.204C3.92645 16.1354 3.84948 16.0438 3.8 15.9291L2.84876 13.7166C2.78066 13.5705 2.69896 13.4324 2.60367 13.3024C2.50838 13.1724 2.40132 13.053 2.28251 12.9441C1.55211 12.2383 0.988818 11.4179 0.592621 10.4827Q-0.00167312 9.08 5e-06 7.55656ZM4.0064 2.37345Q5.5902 1.30623 7.50001 1.30656C8.77332 1.30609 9.93802 1.66154 10.9941 2.37292Q12.5782 3.44 13.2952 5.21028C13.7732 6.39047 13.8805 7.60346 13.6172 8.84924C13.3538 10.095 12.7648 11.1608 11.85 12.0466C11.5213 12.3641 11.2075 12.7516 11.005 13.2228L10.215 15.0566L4.78751 15.0566L3.99626 13.2228C3.79376 12.7516 3.48001 12.3641 3.15126 12.0466C2.23659 11.1609 1.64755 10.0952 1.38415 8.84955C1.12075 7.60388 1.22798 6.39099 1.70583 5.21086Q2.4226 3.44066 4.0064 2.37345Z" fill-rule="evenodd" transform="matrix(1 0 0 1 2.49999 -0.056557)" fill="rgb(114, 45, 209)"/>
      </g>
    </g>
  </svg>

  <!-- 应变能力图标 - 3.svg -->
  <svg v-else-if="name === 'adaptability'" class="svg-icon" viewBox="0 0 20 20">
    <defs>
      <mask id="mask4026271920" style="mask-type:alpha">
        <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
      </mask>
    </defs>
    <g mask="url(#mask4026271920)">
      <defs>
        <mask id="mask7581202187" style="mask-type:alpha">
          <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask7581202187)">
        <path d="M0 0L1.25 0L1.25 18.75L20 18.75L20 20L0 20L0 0ZM18.7474 4.31312C18.7303 4.14125 18.6549 4.00063 18.5212 3.89125C18.3877 3.78174 18.235 3.73553 18.0631 3.75261C17.8912 3.7697 17.7506 3.84508 17.6412 3.97875L12.4537 10.32L9.1925 7.0575C9.05662 6.92175 8.89294 6.86146 8.70146 6.87661C8.50999 6.89177 8.35783 6.97707 8.245 7.1325L3.245 14.0075C3.15125 14.147 3.11901 14.2995 3.14826 14.4649C3.17751 14.6304 3.26008 14.7626 3.39596 14.8615C3.53184 14.9604 3.68302 14.9983 3.84948 14.9752C4.01594 14.9522 4.15112 14.8746 4.255 14.7425L8.825 8.45875L12.0588 11.6925C12.1895 11.823 12.347 11.8836 12.5315 11.8744C12.716 11.8652 12.8667 11.7891 12.9837 11.6462L18.6087 4.77125C18.7183 4.63769 18.7645 4.48498 18.7474 4.31312Z" fill-rule="evenodd" transform="matrix(1 0 0 1 0 0)" fill="rgb(114, 45, 209)"/>
      </g>
    </g>
  </svg>

  <!-- 行为礼仪图标 - 4.svg -->
  <svg v-else-if="name === 'behavior-etiquette'" class="svg-icon" viewBox="0 0 20 20">
    <defs>
      <mask id="mask1897546820" style="mask-type:alpha">
        <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
      </mask>
    </defs>
    <g mask="url(#mask1897546820)">
      <defs>
        <mask id="mask2704790221" style="mask-type:alpha">
          <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask2704790221)">
        <path d="M17.0711 17.0711C15.1184 19.0237 12.7614 20 10 20C7.23858 20 4.88155 19.0237 2.92893 17.0711Q0 14.1421 0 10Q0 5.85786 2.92893 2.92893Q5.85786 0 10 0Q14.1421 0 17.0711 2.92893C19.0237 4.88155 20 7.23858 20 10C20 12.7614 19.0237 15.1184 17.0711 17.0711ZM10 18.75Q6.37563 18.75 3.81281 16.1872Q1.25 13.6244 1.25 10Q1.25 6.37563 3.81281 3.81281Q6.37563 1.25 10 1.25Q13.6244 1.25 16.1872 3.81281Q18.75 6.37563 18.75 10Q18.75 13.6244 16.1872 16.1872Q13.6244 18.75 10 18.75Z" fill-rule="evenodd" transform="matrix(1 0 0 1 0 0)" fill="rgb(114, 45, 209)"/>
        <path d="M2.45779 3.75C3.14779 3.75 3.70779 2.91 3.70779 1.875C3.70779 0.84 3.14779 0 2.45779 0C1.76779 0 1.20779 0.84 1.20779 1.875C1.20779 2.91 1.76779 3.75 2.45779 3.75ZM7.45779 3.75C8.14779 3.75 8.70779 2.91 8.70779 1.875C8.70779 0.84 8.14779 0 7.45779 0C6.76779 0 6.20779 0.84 6.20779 1.875C6.20779 2.91 6.76779 3.75 7.45779 3.75ZM0.788286 5.64631C0.621582 5.60165 0.4635 5.62246 0.314037 5.70875C0.164578 5.79505 0.0675173 5.92155 0.022851 6.08825C-0.0218152 6.25495 -0.00100275 6.41304 0.0852873 6.5625Q0.838759 7.86833 2.14447 8.62201C3.01494 9.12446 3.95271 9.37545 4.95779 9.375C5.96286 9.37545 6.90064 9.12446 7.77111 8.62201Q9.07681 7.86833 9.83029 6.5625C9.91798 6.41291 9.93966 6.2543 9.89534 6.08666Q9.82886 5.83521 9.60361 5.70516C9.45345 5.61846 9.29469 5.59782 9.12735 5.64325C8.96001 5.68868 8.83349 5.78676 8.74779 5.9375Q8.16178 6.95327 7.14613 7.53948Q6.13047 8.12569 4.95779 8.125Q3.7851 8.12569 2.76945 7.53948Q1.7538 6.95327 1.16779 5.9375C1.08149 5.78804 0.95499 5.69098 0.788286 5.64631Z" fill-rule="evenodd" transform="matrix(1 0 0 1 5.04221 6.25)" fill="rgb(114, 45, 209)"/>
      </g>
    </g>
  </svg>

  <!-- 英语沟通图标 - 5.svg -->
  <svg v-else-if="name === 'english-communication'" class="svg-icon" viewBox="0 0 20 20">
    <defs>
      <mask id="mask9000842631" style="mask-type:alpha">
        <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
      </mask>
    </defs>
    <g mask="url(#mask9000842631)">
      <defs>
        <mask id="mask6518720313" style="mask-type:alpha">
          <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask6518720313)">
        <path d="M1.93125 4.6425L1.3875 6.25L0 6.25L2.3275 0L3.9325 0L6.25 6.25L4.79125 6.25L4.2475 4.6425L1.93125 4.6425ZM3.125 1.195L3.97375 3.7225L2.215 3.7225L3.06375 1.195L3.125 1.195Z" fill-rule="evenodd" transform="matrix(1 0 0 1 3.75 3.75)" fill="rgb(114, 45, 209)"/>
        <path d="M0.732233 0.732233Q0 1.46447 0 2.5L0 11.25Q0 12.2855 0.732233 13.0178Q1.46447 13.75 2.5 13.75L6.25 13.75L6.25 17.5Q6.25 18.5355 6.98223 19.2678C7.47039 19.7559 8.05964 20 8.75 20L17.5 20C18.1904 20 18.7796 19.7559 19.2678 19.2678C19.7559 18.7796 20 18.1904 20 17.5L20 8.75C20 8.05964 19.7559 7.47039 19.2678 6.98223Q18.5355 6.25 17.5 6.25L13.75 6.25L13.75 2.5Q13.75 1.46447 13.0178 0.732233Q12.2855 0 11.25 0L2.5 0Q1.46446 0 0.732233 0.732233ZM2.5 1.25C2.15482 1.25 1.86019 1.37204 1.61612 1.61612C1.37204 1.86019 1.25 2.15482 1.25 2.5L1.25 11.25Q1.25 11.7678 1.61612 12.1339C1.86019 12.378 2.15482 12.5 2.5 12.5L11.25 12.5Q11.7678 12.5 12.1339 12.1339Q12.5 11.7678 12.5 11.25L12.5 2.5C12.5 2.15482 12.378 1.86019 12.1339 1.61612Q11.7678 1.25 11.25 1.25L2.5 1.25ZM12.21 14.8013C11.925 14.4725 11.6637 14.12 11.4225 13.7437C11.6315 13.7297 11.8355 13.6899 12.0345 13.6243C12.2335 13.5587 12.4211 13.4693 12.5975 13.3562C12.6785 13.4633 12.7622 13.5681 12.8487 13.6706C12.9352 13.7732 13.0244 13.8734 13.1162 13.9713C13.7813 13.2413 14.3088 12.3638 14.7062 11.3088L13.75 11.3088L13.75 10L17.5 10L17.5 11.3087L16.0013 11.3087C15.5025 12.6912 14.8263 13.8212 13.975 14.755C14.9288 15.4925 16.0938 16.0437 17.5 16.36C17.2313 16.6312 16.88 17.155 16.7138 17.4725C15.225 17.0887 14.0225 16.4637 13.0513 15.6325C12.0437 16.445 10.85 17.0512 9.44375 17.5C9.31375 17.21 8.9725 16.6875 8.75 16.4162C10.1187 16.0525 11.275 15.52 12.21 14.8013Z" fill-rule="evenodd" transform="matrix(1 0 0 1 0 0)" fill="rgb(114, 45, 209)"/>
      </g>
    </g>
  </svg>

  <!-- 文化匹配图标 - 6.svg -->
  <svg v-else-if="name === 'cultural-fit'" class="svg-icon" viewBox="0 0 20 20">
    <defs>
      <mask id="mask2496456916" style="mask-type:alpha">
        <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
      </mask>
    </defs>
    <g mask="url(#mask2496456916)">
      <defs>
        <mask id="mask1955934755" style="mask-type:alpha">
          <rect width="20" height="20" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask1955934755)">
        <path d="M3.75 0.625Q3.75 0.366117 3.93306 0.183059C4.0551 0.06102 4.20241 0 4.375 0L5.625 0Q5.88388 -6.25849e-07 6.06694 0.183058C6.18898 0.305096 6.25 0.452411 6.25 0.625L6.25 1.875Q6.25 2.13388 6.06694 2.31694Q5.88388 2.5 5.625 2.5L4.375 2.5C4.20241 2.5 4.0551 2.43898 3.93306 2.31694Q3.75 2.13388 3.75 1.875L3.75 0.625ZM0.183059 0.183059C0.06102 0.305098 0 0.452411 0 0.625L0 1.875Q-6.25849e-07 2.13388 0.183058 2.31694C0.305096 2.43898 0.452411 2.5 0.625 2.5L1.875 2.5Q2.13388 2.5 2.31694 2.31694C2.43898 2.1949 2.5 2.04759 2.5 1.875L2.5 0.625C2.5 0.452411 2.43898 0.305096 2.31694 0.183058C2.1949 0.0610188 2.04759 0 1.875 0L0.625 0Q0.366117 6.25849e-07 0.183059 0.183059ZM7.68306 0.183059C7.8051 0.06102 7.95241 0 8.125 0L9.375 0C9.54759 0 9.6949 0.0610188 9.81694 0.183058C9.93898 0.305096 10 0.452411 10 0.625L10 1.875Q10 2.13388 9.81694 2.31694C9.6949 2.43898 9.54759 2.5 9.375 2.5L8.125 2.5C7.95241 2.5 7.8051 2.43898 7.68306 2.31694C7.56102 2.1949 7.5 2.04759 7.5 1.875L7.5 0.625Q7.5 0.366117 7.68306 0.183059ZM0 4.375Q6.25849e-07 4.11612 0.183059 3.93306Q0.366117 3.75 0.625 3.75L1.875 3.75Q2.13388 3.75 2.31694 3.93306C2.43898 4.0551 2.5 4.20241 2.5 4.375L2.5 5.625Q2.5 5.88388 2.31694 6.06694Q2.13388 6.25 1.875 6.25L0.625 6.25C0.452411 6.25 0.305096 6.18898 0.183058 6.06694Q0 5.88388 0 5.625L0 4.375ZM4.375 3.75C4.20241 3.75 4.0551 3.81102 3.93306 3.93306Q3.75 4.11612 3.75 4.375L3.75 5.625Q3.75 5.88388 3.93306 6.06694C4.0551 6.18898 4.20241 6.25 4.375 6.25L5.625 6.25Q5.88388 6.25 6.06694 6.06694Q6.25 5.88388 6.25 5.625L6.25 4.375C6.25 4.20241 6.18898 4.0551 6.06694 3.93306Q5.88388 3.75 5.625 3.75L4.375 3.75ZM7.68306 3.93306C7.56102 4.0551 7.5 4.20241 7.5 4.375L7.5 5.625Q7.5 5.88388 7.68306 6.06694C7.8051 6.18898 7.95241 6.25 8.125 6.25L9.375 6.25C9.54759 6.25 9.6949 6.18898 9.81694 6.06694C9.93898 5.9449 10 5.79759 10 5.625L10 4.375C10 4.20241 9.93898 4.0551 9.81694 3.93306C9.6949 3.81102 9.54759 3.75 9.375 3.75L8.125 3.75C7.95241 3.75 7.8051 3.81102 7.68306 3.93306ZM0.183058 7.68306C0.305096 7.56102 0.452411 7.5 0.625 7.5L1.875 7.5Q2.13388 7.5 2.31694 7.68306C2.43898 7.8051 2.5 7.95241 2.5 8.125L2.5 9.375C2.5 9.54759 2.43898 9.6949 2.31694 9.81694C2.1949 9.93898 2.04759 10 1.875 10L0.625 10C0.452411 10 0.305096 9.93898 0.183058 9.81694C0.0610188 9.6949 0 9.54759 0 9.375L0 8.125C0 7.95241 0.0610188 7.8051 0.183058 7.68306ZM3.75 8.125C3.75 7.95241 3.81102 7.8051 3.93306 7.68306C4.0551 7.56102 4.20241 7.5 4.375 7.5L5.625 7.5Q5.88388 7.5 6.06694 7.68306C6.18898 7.8051 6.25 7.95241 6.25 8.125L6.25 9.375C6.25 9.54759 6.18898 9.6949 6.06694 9.81694Q5.88388 10 5.625 10L4.375 10C4.20241 10 4.0551 9.93898 3.93306 9.81694C3.81102 9.6949 3.75 9.54759 3.75 9.375L3.75 8.125ZM7.68306 7.68306C7.8051 7.56102 7.95241 7.5 8.125 7.5L9.375 7.5C9.54759 7.5 9.6949 7.56102 9.81694 7.68306C9.93898 7.8051 10 7.95241 10 8.125L10 9.375C10 9.54759 9.93898 9.6949 9.81694 9.81694C9.6949 9.93898 9.54759 10 9.375 10L8.125 10C7.95241 10 7.8051 9.93898 7.68306 9.81694C7.56102 9.6949 7.5 9.54759 7.5 9.375L7.5 8.125C7.5 7.95241 7.56102 7.8051 7.68306 7.68306Z" fill-rule="evenodd" transform="matrix(1 0 0 1 5 2.5)" fill="rgb(114, 45, 209)"/>
        <path d="M0 1.25C0 0.904822 0.122039 0.610194 0.366116 0.366116C0.610194 0.122039 0.904822 0 1.25 0L13.75 0Q14.2678 0 14.6339 0.366116C14.878 0.610194 15 0.904822 15 1.25L15 18.75C15 19.0952 14.878 19.3898 14.6339 19.6339C14.3898 19.878 14.0952 20 13.75 20L1.25 20C0.904822 20 0.610194 19.878 0.366116 19.6339C0.122039 19.3898 0 19.0952 0 18.75L0 1.25ZM1.25 1.25L13.75 1.25L13.75 18.75L10 18.75L10 15.625C10 15.4524 9.93898 15.3051 9.81694 15.1831C9.6949 15.061 9.54759 15 9.375 15L5.625 15C5.45241 15 5.3051 15.061 5.18306 15.1831C5.06102 15.3051 5 15.4524 5 15.625L5 18.75L1.25 18.75L1.25 1.25Z" fill-rule="evenodd" transform="matrix(1 0 0 1 2.5 0)" fill="rgb(114, 45, 209)"/>
      </g>
    </g>
  </svg>

  <!-- 优势图标 - 7.svg -->
  <svg v-else-if="name === 'advantages'" class="svg-icon" viewBox="0 0 20 20">
    <path d="M14.375 7.5Q14.375 6.10151 13.8348 4.82485Q13.3128 3.59125 12.3608 2.63921Q11.4087 1.68716 10.1752 1.16519Q8.89849 0.625 7.5 0.625Q6.10151 0.625 4.82485 1.16519Q3.59125 1.68717 2.63921 2.63921Q1.68717 3.59125 1.16519 4.82485Q0.625 6.10151 0.625 7.5Q0.625 8.89849 1.16519 10.1752Q1.68716 11.4087 2.63921 12.3608Q3.59125 13.3128 4.82485 13.8348Q6.10151 14.375 7.5 14.375Q8.89849 14.375 10.1752 13.8348Q11.4087 13.3128 12.3608 12.3608Q13.3128 11.4087 13.8348 10.1752Q14.375 8.89849 14.375 7.5ZM15.625 7.5Q15.625 9.15206 14.986 10.6623Q14.3691 12.1203 13.2447 13.2447Q12.1203 14.3691 10.6623 14.986Q9.15206 15.625 7.5 15.625Q5.84794 15.625 4.33775 14.986Q2.8797 14.3691 1.75532 13.2447Q0.630949 12.1203 0.0140059 10.6623Q-0.625 9.15206 -0.625 7.5Q-0.625 5.84794 0.0140059 4.33775Q0.630949 2.8797 1.75532 1.75532Q2.8797 0.63095 4.33775 0.0140059Q5.84794 -0.625 7.5 -0.625Q9.15206 -0.625 10.6623 0.0140059Q12.1203 0.630949 13.2447 1.75532Q14.369 2.8797 14.986 4.33775Q15.625 5.84794 15.625 7.5Z" transform="matrix(1 0 0 1 2.5 2.5)" fill="currentColor"/>
    <path d="M7.902 -0.478566C7.63769 -0.700579 7.24345 -0.666297 7.02143 -0.401995L2.23373 5.29765L0.464559 3.3319C0.233649 3.07533 -0.161537 3.05453 -0.418103 3.28544C-0.67467 3.51635 -0.695469 3.91154 -0.464559 4.1681L1.78544 6.6681Q1.81461 6.70052 1.84801 6.72857Q1.9432 6.80853 2.06174 6.84597Q2.18029 6.88342 2.30414 6.87265Q2.428 6.86188 2.5383 6.80453Q2.6486 6.74719 2.72857 6.652L7.97857 0.401995C8.20058 0.137693 8.1663 -0.256552 7.902 -0.478566Z" transform="matrix(1 0 0 1 6.25 6.875)" fill="currentColor"/>
  </svg>

  <!-- 劣势图标 - 8.svg -->
  <svg v-else-if="name === 'disadvantages'" class="svg-icon" viewBox="0 0 20 20">
    <path d="M14.375 7.5Q14.375 6.10151 13.8348 4.82485Q13.3128 3.59125 12.3608 2.63921Q11.4087 1.68716 10.1752 1.16519Q8.89849 0.625 7.5 0.625Q6.10151 0.625 4.82485 1.16519Q3.59125 1.68717 2.63921 2.63921Q1.68717 3.59125 1.16519 4.82485Q0.625 6.10151 0.625 7.5Q0.625 8.89849 1.16519 10.1752Q1.68716 11.4087 2.63921 12.3608Q3.59125 13.3128 4.82485 13.8348Q6.10151 14.375 7.5 14.375Q8.89849 14.375 10.1752 13.8348Q11.4087 13.3128 12.3608 12.3608Q13.3128 11.4087 13.8348 10.1752Q14.375 8.89849 14.375 7.5ZM15.625 7.5Q15.625 9.15206 14.986 10.6623Q14.3691 12.1203 13.2447 13.2447Q12.1203 14.3691 10.6623 14.986Q9.15206 15.625 7.5 15.625Q5.84794 15.625 4.33775 14.986Q2.8797 14.3691 1.75532 13.2447Q0.630949 12.1203 0.0140059 10.6623Q-0.625 9.15206 -0.625 7.5Q-0.625 5.84794 0.0140059 4.33775Q0.630949 2.8797 1.75532 1.75532Q2.8797 0.63095 4.33775 0.0140059Q5.84794 -0.625 7.5 -0.625Q9.15206 -0.625 10.6623 0.0140059Q12.1203 0.630949 13.2447 1.75532Q14.369 2.8797 14.986 4.33775Q15.625 5.84794 15.625 7.5Z" transform="matrix(1 0 0 1 2.5 2.5)" fill="currentColor"/>
    <path d="M0.624309 0.20499L0.848528 4.96866L0.224219 4.99805L-0.400092 4.96871L-0.176263 0.205041Q-0.184132 0.378374 -0.0643369 0.502513Q0.055454 0.626648 0.227956 0.62497Q0.398 0.623369 0.515039 0.499736Q0.632076 0.376104 0.624357 0.206037L0.624309 0.20499ZM-0.624357 0.262713Q-0.640713 -0.0976422 -0.392719 -0.359609Q-0.144725 -0.621575 0.215797 -0.62497Q0.581316 -0.628526 0.835146 -0.36549Q1.08898 -0.102451 1.07236 0.263709L0.84853 5.02738Q0.845815 5.08517 0.832539 5.14148Q0.819262 5.19778 0.795875 5.2507Q0.772489 5.30361 0.739787 5.35134Q0.707085 5.39906 0.666179 5.43997Q0.625273 5.48088 0.577553 5.51358Q0.529833 5.54629 0.47692 5.56968Q0.424007 5.59307 0.3677 5.60635Q0.311393 5.61963 0.253605 5.62235Q0.192115 5.62525 0.131243 5.61609Q0.0703711 5.60693 0.012455 5.58608Q-0.045461 5.56522 -0.0981952 5.53346Q-0.150929 5.50171 -0.196455 5.46028Q-0.241981 5.41884 -0.278549 5.36933Q-0.315117 5.31981 -0.341322 5.26411Q-0.367526 5.20841 -0.382361 5.14866Q-0.397195 5.08892 -0.40009 5.02743L-0.624309 0.26376L-0.624357 0.262713Z" transform="matrix(1 0 0 1 9.77578 6.25195)" fill="currentColor"/>
    <circle cx="10" cy="13.59025" r="0.78125" fill="currentColor"/>
  </svg>

  <!-- 简历上传图标 - 用于ResumeManagement.vue简历上传区域 -->
  <svg v-else-if="name === 'resume-upload'" class="svg-icon" viewBox="0 0 64 64">
    <defs>
      <mask id="mask4712830420" style="mask-type:alpha">
        <rect width="64" height="64" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
      </mask>
    </defs>
    <g mask="url(#mask4712830420)">
      <defs>
        <mask id="mask8609573006" style="mask-type:alpha">
          <rect width="64" height="64" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask8609573006)">
        <path d="M47.5 36L38 36C36.8954 36 36 36.8954 36 38C36 39.1046 36.8954 40 38 40L47.5 40Q50.3929 40 52.9286 39.2388Q55.5752 38.4443 57.5562 36.9031Q59.6729 35.2563 60.8104 32.9295Q62 30.4963 62 27.55Q62 24.599 60.6319 22.12Q59.4094 19.9048 57.2082 18.2698Q55.2626 16.8246 52.7775 15.9925Q51.2858 15.4931 49.7677 15.2674Q49.2059 11.9447 47.7989 9.06461Q46.1096 5.60681 43.3551 3.0871Q40.654 0.616255 37.2176 -0.692379Q33.7839 -2 30 -2Q26.5432 -2 23.4484 -0.901819Q20.5843 0.114543 18.2041 2.00542Q16.0023 3.75456 14.4033 6.09625Q13.2684 7.75823 12.5381 9.56598Q10.1082 9.93631 7.90172 10.8244Q5.02474 11.9822 2.8649 13.8863Q0.563024 15.9156 -0.679683 18.5796Q-2 21.41 -2 24.7Q-2 28.0168 -0.624935 30.929Q0.689844 33.7135 3.09897 35.7942Q5.44218 37.818 8.50686 38.9057Q11.5903 40 15 40L22 40C23.1046 40 24 39.1046 24 38C24 36.8954 23.1046 36 22 36L15 36Q12.2791 36 9.84471 35.136Q7.4881 34.2997 5.71353 32.767Q3.94531 31.2398 2.99212 29.2211Q2 27.12 2 24.7Q2 22.2971 2.94531 20.2706Q3.83486 18.3637 5.5101 16.8868Q7.155 15.4367 9.39516 14.5351Q11.6349 13.6337 14.1891 13.391Q14.3345 13.3772 14.4763 13.3425Q14.618 13.3077 14.7533 13.2527Q14.8885 13.1977 15.0143 13.1237Q15.1401 13.0497 15.2539 12.9581Q15.3676 12.8666 15.4668 12.7595Q15.5661 12.6525 15.6487 12.5321Q15.7314 12.4118 15.7956 12.2807Q15.8599 12.1497 15.9045 12.0106Q16.5208 10.0884 17.7066 8.35192Q18.9643 6.51007 20.6922 5.1374Q22.5503 3.66126 24.7861 2.86787Q27.2318 2 30 2Q33.048 2 35.7941 3.04574Q38.5172 4.08278 40.6552 6.03853Q42.8491 8.04538 44.2049 10.8205Q45.6398 13.7575 46.0108 17.3079Q46.0201 17.396 46.037 17.483Q46.054 17.57 46.0786 17.6552Q46.1032 17.7403 46.1352 17.823Q46.1673 17.9056 46.2065 17.9851Q46.2457 18.0646 46.2918 18.1403Q46.3379 18.216 46.3905 18.2873Q46.4431 18.3586 46.5019 18.425Q46.5606 18.4914 46.6249 18.5523Q46.6893 18.6133 46.7588 18.6683Q46.8283 18.7233 46.9024 18.7719Q46.9765 18.8205 47.0546 18.8624Q47.1327 18.9043 47.2142 18.9392Q47.2957 18.974 47.38 19.0015Q47.4642 19.0289 47.5506 19.0489Q47.637 19.0688 47.7247 19.081Q47.8125 19.0932 47.9011 19.0976Q49.722 19.1878 51.5076 19.7856Q53.3887 20.4154 54.823 21.4808Q56.3269 22.598 57.1298 24.0528Q58 25.6295 58 27.55Q58 29.5708 57.2169 31.1726Q56.4859 32.6677 55.1 33.746Q53.7091 34.8282 51.7785 35.4077Q49.8055 36 47.5 36Z" fill-rule="evenodd" transform="matrix(1 0 0 1 2 7.97375)" fill="rgb(156, 163, 175)"/>
        <path d="M10 4.82843L14.5858 9.41421L17.4142 6.58579L8 -2.82843L-1.41421 6.58579L1.41421 9.41421L6 4.82843L6 32.0525L10 32.0525L10 4.82843Z" fill-rule="evenodd" transform="matrix(1 0 0 1 24 23.9737)" fill="rgb(156, 163, 175)"/>
      </g>
    </g>
  </svg>

  <!-- 合作握手图标 - 用于Report.vue协作意识 -->
  <svg v-else-if="name === 'collaboration'" class="svg-icon" viewBox="0 0 20 20">
    <defs>
      <clipPath id="clipPath7036848141">
        <path d="M0 0L20 0L20 20L0 20L0 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 0 0)"/>
      </clipPath>
    </defs>
    <g clip-path="url(#clipPath7036848141)">
      <path d="M7.62623 4.04044L5 1.41421L1.41421 5L8.33333 11.9191L12.6262 7.62623Q12.7669 7.48557 12.9507 7.40945Q13.1344 7.33333 13.3333 7.33333Q13.5322 7.33333 13.716 7.40945Q13.8998 7.48557 14.0404 7.62623Q14.1101 7.69587 14.1648 7.77776Q14.2195 7.85965 14.2572 7.95065Q14.2949 8.04164 14.3141 8.13824Q14.3333 8.23484 14.3333 8.33333Q14.3333 8.43182 14.3141 8.52842Q14.2949 8.62502 14.2572 8.71601Q14.2195 8.80701 14.1648 8.8889Q14.1101 8.97079 14.0404 9.04044L9.04044 14.0404Q8.97079 14.1101 8.8889 14.1648Q8.80701 14.2195 8.71601 14.2572Q8.62502 14.2949 8.52842 14.3141Q8.43182 14.3333 8.33333 14.3333Q8.23484 14.3333 8.13824 14.3141Q8.04164 14.2949 7.95065 14.2572Q7.85965 14.2195 7.77776 14.1648Q7.69587 14.1101 7.62623 14.0404L-0.707107 5.70711Q-0.847759 5.56645 -0.92388 5.38268Q-1 5.19891 -1 5Q-1 4.80109 -0.923879 4.61732Q-0.847759 4.43355 -0.707107 4.29289L4.29289 -0.707107Q4.43355 -0.847759 4.61732 -0.92388Q4.80109 -1 5 -1Q5.19891 -1 5.38268 -0.92388Q5.56645 -0.847759 5.70711 -0.707107L9.04044 2.62623C9.43096 3.01675 9.43096 3.64992 9.04044 4.04044C8.64992 4.43096 8.01675 4.43096 7.62623 4.04044Z" fill-rule="evenodd" transform="matrix(1 0 0 1 1.66669 3.33333)" fill="rgb(114, 45, 209)"/>
      <path d="M-0.707107 5.54289Q-0.794156 5.62994 -0.857493 5.7355Q-1 5.97302 -1 6.25L-0.999999 6.25119Q-0.999854 6.37369 -0.970143 6.49254Q-0.902964 6.76125 -0.707107 6.95711L-0.706266 6.95795Q-0.619541 7.04447 -0.514496 7.10749L1.56884 8.35749Q1.64006 8.40022 1.71735 8.43062Q1.79464 8.46102 1.87589 8.47825Q1.95713 8.49548 2.04011 8.49907Q2.12309 8.50266 2.20552 8.49251Q2.28795 8.48236 2.36758 8.45875Q2.4472 8.43515 2.52185 8.39873Q2.59649 8.3623 2.6641 8.31407Q2.73171 8.26583 2.79044 8.20711L4.58333 6.41421L7.20956 9.04044Q7.2792 9.11008 7.3611 9.1648Q7.44299 9.21952 7.53398 9.25721Q7.62498 9.2949 7.72158 9.31412Q7.81818 9.33333 7.91667 9.33333Q8.01516 9.33333 8.11176 9.31412Q8.20835 9.2949 8.29935 9.25721Q8.39034 9.21952 8.47224 9.1648Q8.55413 9.11008 8.62377 9.04044L11.9571 5.70711Q12.0268 5.63746 12.0815 5.55557Q12.1362 5.47368 12.1739 5.38268Q12.2116 5.29169 12.2308 5.19509Q12.25 5.09849 12.25 5Q12.25 4.90151 12.2308 4.80491Q12.2116 4.70831 12.1739 4.61732Q12.1362 4.52632 12.0815 4.44443Q12.0268 4.36254 11.9571 4.29289L6.95711 -0.707107Q6.81645 -0.847759 6.63268 -0.92388Q6.44891 -1 6.25 -1Q6.05109 -1 5.86732 -0.923879Q5.68355 -0.847759 5.54289 -0.707107L-0.707107 5.54289ZM1.61275 6.05146L6.25 1.41421L9.83579 5L7.91667 6.91912L5.29044 4.29289Q5.2208 4.22325 5.1389 4.16853Q5.05701 4.11381 4.96602 4.07612Q4.87502 4.03843 4.77842 4.01921Q4.68182 4 4.58333 4Q4.48484 4 4.38824 4.01921Q4.29164 4.03843 4.20065 4.07612Q4.10965 4.11381 4.02776 4.16853Q3.94587 4.22325 3.87623 4.29289L1.92832 6.2408L1.61275 6.05146Z" fill-rule="evenodd" transform="matrix(1 0 0 1 7.08331 3.33333)" fill="rgb(114, 45, 209)"/>
      <path d="M-0.707107 -0.707107C-1.09763 -0.316586 -1.09763 0.316586 -0.707107 0.707107L0.542893 1.95711C0.933414 2.34763 1.56659 2.34763 1.95711 1.95711C2.34763 1.56659 2.34763 0.933414 1.95711 0.542893L0.707107 -0.707107C0.316586 -1.09763 -0.316586 -1.09763 -0.707107 -0.707107Z" fill-rule="evenodd" transform="matrix(1 0 0 1 10.4167 13.75)" fill="rgb(114, 45, 209)"/>
      <path d="M-0.707107 -0.707107C-1.09763 -0.316586 -1.09763 0.316586 -0.707107 0.707107L0.542893 1.95711C0.933414 2.34763 1.56659 2.34763 1.95711 1.95711C2.34763 1.56659 2.34763 0.933414 1.95711 0.542893L0.707107 -0.707107C0.316586 -1.09763 -0.316586 -1.09763 -0.707107 -0.707107Z" fill-rule="evenodd" transform="matrix(1 0 0 1 12.0833 12.0833)" fill="rgb(114, 45, 209)"/>
    </g>
  </svg>











</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    name: {
      type: String,
      required: true
    }
  }
}
</script>

<style scoped>
.svg-icon {
  width: 100%;
  height: 100%;
}
</style> 