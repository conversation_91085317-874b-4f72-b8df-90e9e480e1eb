<template>
  <!-- 面试模式图标1 - FRAME 1.svg -->
  <svg v-if="name === 'interview-mode-1'" class="svg-icon" viewBox="0 0 26 29">
    <defs>
      <filter id="filter4593245933" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feComponentTransfer in="SourceAlpha" result="componentTransfer7147227000">
          <feFuncR type="linear" slope="0" intercept="0"/>
          <feFuncG type="linear" slope="0" intercept="0"/>
          <feFuncB type="linear" slope="0" intercept="0"/>
          <feFuncA type="linear" slope="10000" intercept="0"/>
        </feComponentTransfer>
        <feComponentTransfer in="componentTransfer7147227000" result="componentTransfer4119033907">
          <feFuncA type="linear" slope="0.2" intercept="0"/>
        </feComponentTransfer>
        <feOffset dx="0" dy="4" in="componentTransfer4119033907" result="offset7545941841"/>
        <feGaussianBlur stdDeviation="2" in="offset7545941841" result="gaussianBlur6229193002"/>
        <feMerge>
          <feMergeNode in="gaussianBlur6229193002"/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>
    </defs>
    <g filter="url(#filter4593245933)">
      <defs>
        <mask id="mask8841088900" style="mask-type:alpha">
          <rect width="24" height="24" transform="matrix(1 0 0 1 1 0)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask8841088900)">
        <defs>
          <mask id="mask0948326418" style="mask-type:alpha">
            <rect width="24" height="24" transform="matrix(1 0 0 1 1 0)" fill="rgb(0, 0, 0)"/>
          </mask>
        </defs>
        <g mask="url(#mask0948326418)">
          <path d="M2.83 3.59L5 5.76L3.59 7.18L0 3.59L3.59 0L5 1.42L2.83 3.59ZM12 3.59L8.41 7.18L7 5.76L9.17 3.59L7 1.42L8.41 0L12 3.59Z" fill-rule="evenodd" transform="matrix(1 0 0 1 7 8.41)" fill="currentColor"/>
          <path d="M16 2L11.82 2C11.4 0.84 10.3 0 9 0C7.7 0 6.6 0.84 6.18 2L2 2C1.86 2 1.73 2.01 1.6 2.04C1.27674 2.10782 0.987802 2.24694 0.733191 2.45735C0.47858 2.66776 0.287516 2.92531 0.16 3.23C0.06 3.46 0 3.72 0 4L0 18C0 18.27 0.06 18.54 0.16 18.78C0.26 19.02 0.41 19.23 0.59 19.42C0.86 19.69 1.21 19.89 1.6 19.97C1.73 19.99 1.86 20 2 20L16 20C17.1 20 18 19.1 18 18L18 4C18 2.9 17.1 2 16 2ZM9.75 2.5C9.75 2.09 9.41 1.75 9 1.75C8.59 1.75 8.25 2.09 8.25 2.5C8.25 2.91 8.59 3.25 9 3.25C9.41 3.25 9.75 2.91 9.75 2.5ZM16 18L16 4L2 4L2 18L16 18Z" fill-rule="evenodd" transform="matrix(1 0 0 1 4 1)" fill="currentColor"/>
        </g>
      </g>
    </g>
  </svg>

  <!-- 面试模式图标2 - FRAME 2.svg -->
  <svg v-else-if="name === 'interview-mode-2'" class="svg-icon" viewBox="0 0 24 24">
    <defs>
      <mask id="mask0070305984" style="mask-type:alpha">
        <rect width="24" height="24" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
      </mask>
    </defs>
    <g mask="url(#mask0070305984)">
      <defs>
        <mask id="mask7065509681" style="mask-type:alpha">
          <rect width="24" height="24" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask7065509681)">
        <path d="M1.43 0C1.03512 0 0.698062 0.139613 0.418837 0.418837C0.139612 0.698061 -1e-06 1.03512 0 1.43C-1e-06 1.82488 0.139612 2.16194 0.418837 2.44116C0.698062 2.72039 1.03512 2.86 1.43 2.86C1.82488 2.86 2.16194 2.72039 2.44116 2.44116C2.72039 2.16194 2.86 1.82488 2.86 1.43C2.86 1.03512 2.72039 0.698061 2.44116 0.418837C2.16194 0.139613 1.82488 0 1.43 0Z" fill-rule="nonzero" transform="matrix(1 0 0 1 11.57 8.57)" fill="currentColor"/>
        <path d="M9.00011 0C5.25011 0 2.20011 2.94 2.02011 6.64L0.100113 9.2C0.043297 9.27575 0.010637 9.36079 0.002133 9.4551C-0.006371 9.54941 0.010552 9.63891 0.0529 9.72361C0.095248 9.8083 0.156696 9.87554 0.237247 9.92532Q0.358073 10 0.500113 10L2.00011 10L2.00011 13C2.00011 14.1 2.90011 15 4.00011 15L5.00011 15L5.00011 18L12.0001 18L12.0001 13.32Q14.2157 12.2667 15.2888 10.0606Q16.362 7.85447 15.8229 5.46117Q15.2839 3.06787 13.3686 1.53484Q11.4534 0.0018075 9.00011 0ZM11.9801 7.39C11.9901 7.26 12.0001 7.13 12.0001 7C12.0001 6.86 11.9901 6.73 11.9601 6.61L12.8101 5.95C12.85 5.91918 12.8748 5.87899 12.8843 5.82946C12.8938 5.77992 12.8858 5.73344 12.8601 5.69L12.0601 4.31C12.0201 4.22 11.9101 4.19 11.8201 4.22L10.8201 4.62C10.6101 4.46 10.3901 4.33 10.1501 4.23L10.0001 3.17C9.98011 3.07 9.90011 3 9.80011 3L8.20011 3C8.10011 3 8.02011 3.07 8.00011 3.17L7.85011 4.23C7.61011 4.33 7.38011 4.47 7.18011 4.62L6.18011 4.22C6.09011 4.19 5.99011 4.22 5.94011 4.31L5.14011 5.69C5.09011 5.79 5.11011 5.89 5.19011 5.95L6.04011 6.61C6.02011 6.73 6.00011 6.87 6.00011 7C6.00011 7.13 6.01011 7.26 6.02011 7.39L5.18011 8.05C5.14091 8.07906 5.11653 8.11752 5.10696 8.16537C5.09739 8.21322 5.10511 8.2581 5.13011 8.3L5.93011 9.69C5.98011 9.78 6.09011 9.81 6.18011 9.78L7.17011 9.38C7.38011 9.54 7.60011 9.67 7.85011 9.77L8.00011 10.83C8.02011 10.93 8.10011 11 8.20011 11L9.80011 11C9.90011 11 9.99011 10.93 10.0001 10.83L10.1601 9.77C10.4001 9.67 10.6201 9.54 10.8301 9.38L11.8201 9.78C11.9001 9.81 12.0101 9.78 12.0601 9.69L12.8601 8.3C12.9101 8.21 12.8901 8.11 12.8101 8.05L11.9801 7.39Z" fill-rule="evenodd" transform="matrix(1 0 0 1 3.99989 3)" fill="currentColor"/>
      </g>
    </g>
  </svg>

  <!-- 面试模式图标3 - FRAME 3.svg -->
  <svg v-else-if="name === 'interview-mode-3'" class="svg-icon" viewBox="0 0 23.75 26">
    <defs>
      <mask id="mask3830416120" style="mask-type:alpha">
        <rect width="23.75" height="26" transform="matrix(1 0 0 1 0 0)" fill="rgb(0, 0, 0)"/>
      </mask>
    </defs>
    <g mask="url(#mask3830416120)">
      <defs>
        <mask id="mask3419588046" style="mask-type:alpha">
          <rect width="24" height="24" transform="matrix(1 0 0 1 -0.125 1)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask3419588046)">
        <rect width="16" height="20" rx="1" ry="1" stroke-width="2" transform="matrix(1 0 0 1 3.875 3)" stroke="currentColor" fill="transparent"/>
        <path d="M0 -1L4.5 -1Q4.59849 -1 4.69509 -0.980785Q4.79169 -0.96157 4.88268 -0.923879Q4.97368 -0.886188 5.05557 -0.83147Q5.13746 -0.776751 5.20711 -0.707107Q5.27675 -0.637463 5.33147 -0.55557Q5.38619 -0.473678 5.42388 -0.382683Q5.46157 -0.291689 5.48078 -0.19509Q5.5 -0.0984914 5.5 0L5.5 8Q5.5 8.09849 5.48078 8.19509Q5.46157 8.29169 5.42388 8.38268Q5.38619 8.47368 5.33147 8.55557Q5.27675 8.63746 5.20711 8.70711Q5.13746 8.77675 5.05557 8.83147Q4.97368 8.88619 4.88268 8.92388Q4.79169 8.96157 4.69509 8.98079Q4.59849 9 4.5 9Q4.31631 9 4.14462 8.93472Q3.97292 8.86944 3.83564 8.74741L1.58564 6.74741L2.25 6L2.91436 6.74741L0.664364 8.74741Q0.59075 8.81284 0.505786 8.86266Q0.420821 8.91247 0.327771 8.94476Q0.234721 8.97704 0.13716 8.99055Q0.0395994 9.00406 -0.058722 8.99827Q-0.157043 8.99249 -0.252347 8.96764Q-0.347651 8.94278 -0.436275 8.89981Q-0.524899 8.85684 -0.603437 8.79741Q-0.681975 8.73798 -0.747409 8.66436Q-0.869443 8.52708 -0.934722 8.35538Q-1 8.18369 -1 8L-1 0Q-1 -0.0984914 -0.980785 -0.19509Q-0.96157 -0.291689 -0.923879 -0.382683Q-0.886188 -0.473678 -0.83147 -0.55557Q-0.776751 -0.637463 -0.707107 -0.707107Q-0.637463 -0.776751 -0.55557 -0.831469Q-0.473678 -0.886188 -0.382683 -0.923879Q-0.291689 -0.961571 -0.19509 -0.980785Q-0.0984914 -1 0 -1ZM0 1L0 0L1 0L1 8L0 8L-0.664364 7.25259L1.58564 5.25259Q1.65371 5.19208 1.7316 5.14486Q1.80948 5.09765 1.89462 5.06528Q1.97975 5.03291 2.06934 5.01646Q2.15892 5 2.25 5Q2.34108 5 2.43066 5.01646Q2.52025 5.03291 2.60538 5.06528Q2.69052 5.09765 2.7684 5.14486Q2.84629 5.19208 2.91436 5.25259L5.16436 7.25259L4.5 8L3.5 8L3.5 0L4.5 0L4.5 1L0 1Z" fill-rule="nonzero" transform="matrix(1 0 0 1 7.875 3)" fill="currentColor"/>
        <path d="M-1 0C-1 0.55228 -0.55228 1 0 1L5 1C5.55228 1 6 0.55228 6 0C6 -0.55228 5.55228 -1 5 -1L0 -1C-0.55228 -1 -1 -0.55228 -1 0Z" fill-rule="evenodd" transform="matrix(1 0 0 1 7.875 15)" fill="currentColor"/>
        <path d="M-1 0C-1 0.55228 -0.55228 1 0 1L8 1C8.55228 1 9 0.55228 9 0C9 -0.55228 8.55228 -1 8 -1L0 -1C-0.55228 -1 -1 -0.55228 -1 0Z" fill-rule="evenodd" transform="matrix(1 0 0 1 7.875 18)" fill="currentColor"/>
      </g>
    </g>
  </svg>

  <!-- 面试模式图标4 - FRAME 4.svg -->
  <svg v-else-if="name === 'interview-mode-4'" class="svg-icon" viewBox="0 0 30.5 26.75">
    <defs>
      <filter id="filter1105645891" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feComponentTransfer in="SourceAlpha" result="componentTransfer5726211942">
          <feFuncR type="linear" slope="0" intercept="0"/>
          <feFuncG type="linear" slope="0" intercept="0"/>
          <feFuncB type="linear" slope="0" intercept="0"/>
          <feFuncA type="linear" slope="10000" intercept="0"/>
        </feComponentTransfer>
        <feComponentTransfer in="componentTransfer5726211942" result="componentTransfer0342314365">
          <feFuncA type="linear" slope="0.2" intercept="0"/>
        </feComponentTransfer>
        <feOffset dx="0" dy="4" in="componentTransfer0342314365" result="offset1205008403"/>
        <feGaussianBlur stdDeviation="2" in="offset1205008403" result="gaussianBlur5000056178"/>
        <feMerge>
          <feMergeNode in="gaussianBlur5000056178"/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>
    </defs>
    <g filter="url(#filter1105645891)">
      <defs>
        <mask id="mask6350633517" style="mask-type:alpha">
          <rect width="24" height="24" transform="matrix(1 0 0 1 3.25 0)" fill="rgb(0, 0, 0)"/>
        </mask>
      </defs>
      <g mask="url(#mask6350633517)">
        <defs>
          <mask id="mask8067356922" style="mask-type:alpha">
            <rect width="24" height="24" transform="matrix(1 0 0 1 3.25 0)" fill="rgb(0, 0, 0)"/>
          </mask>
        </defs>
        <g mask="url(#mask8067356922)">
          <path d="M0.749984 7.06389L4.62702 9.79295L4.19531 10.4063L4.49999 9.72093Q4.51304 9.72673 4.51813 9.72768Q4.51098 9.72634 4.5005 9.72717Q4.49001 9.728 4.48316 9.73045Q4.48803 9.7287 4.5 9.72093Q4.51197 9.71315 4.51555 9.7094Q4.51052 9.71467 4.5055 9.72392Q4.50048 9.73317 4.49881 9.74025Q4.5 9.73521 4.5 9.72094L4.5 0.685312Q4.5 0.671045 4.49881 0.666002Q4.50048 0.673091 4.5055 0.682336Q4.51052 0.69158 4.51555 0.696849Q4.51197 0.6931 4.5 0.685323Q4.48803 0.677545 4.48315 0.675797Q4.49001 0.678253 4.50049 0.679082Q4.51098 0.679912 4.51814 0.678564Q4.51304 0.679523 4.5 0.685322L4.19531 0L4.62702 0.613297L0.749984 3.34236L0.749997 3.34235Q0.75 3.34234 0.75 3.34234L0.75 7.06406Q0.75 7.0639 0.75 7.0639Q0.75 7.0639 0.749997 7.0639L0.749984 7.06389ZM-0.113434 8.29048Q-0.412696 8.07981 -0.581308 7.755Q-0.749923 7.43019 -0.75 7.06406L-0.75 3.34204Q-0.749926 2.97606 -0.581309 2.65125Q-0.412694 2.32644 -0.113421 2.11577L3.76361 -0.613297Q3.8236 -0.655521 3.89063 -0.685322Q4.25062 -0.845369 4.61878 -0.816247Q4.98694 -0.787125 5.31729 -0.572471Q5.64764 -0.357817 5.82382 -0.0332378Q6 0.291342 6 0.685312L6 9.72094Q6 10.1149 5.82382 10.4395Q5.64764 10.7641 5.31729 10.9787Q4.98694 11.1934 4.61878 11.2225Q4.25062 11.2516 3.89063 11.0916Q3.8236 11.0618 3.76361 11.0195L-0.113421 8.29049L-0.113434 8.29048Z" fill-rule="nonzero" transform="matrix(1 0 0 1 20.5 6.79688)" fill="currentColor"/>
          <path d="M11.0625 12.75L2.4375 12.75Q1.11751 12.7462 0.185647 11.8144Q-0.746212 10.8825 -0.749997 9.56465L-0.75 2.4375Q-0.746212 1.1175 0.185647 0.185646Q1.1175 -0.746212 2.43535 -0.749997L11.085 -0.75Q12.3957 -0.745953 13.3208 0.179191Q14.246 1.10434 14.25 2.41268L14.25 9.5625Q14.2462 10.8825 13.3144 11.8144Q12.3825 12.7462 11.0647 12.75L11.0625 12.75ZM11.0603 11.25Q11.7594 11.248 12.2537 10.7537Q12.748 10.2594 12.75 9.5625L12.75 2.41732Q12.7479 1.72758 12.2601 1.23985Q11.7724 0.752128 11.085 0.75L2.43965 0.749997Q1.74061 0.752004 1.24631 1.24631Q0.752005 1.74061 0.75 2.4375L0.749997 9.56035Q0.752005 10.2594 1.24631 10.7537Q1.74061 11.248 2.4375 11.25L11.0625 11.25L11.0603 11.25Z" fill-rule="nonzero" transform="matrix(1 0 0 1 4.75 6)" fill="currentColor"/>
        </g>
      </g>
    </g>
  </svg>
</template>

<script>
export default {
  name: 'SvgIcon2',
  props: {
    name: {
      type: String,
      required: true
    }
  }
}
</script>

<style scoped>
.svg-icon {
  width: 100%;
  height: 100%;
}
</style> 