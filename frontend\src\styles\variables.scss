// 颜色变量
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// Interview主题色
$interview-primary: #722ED1;
$interview-primary-light: #F9F0FF;
$interview-primary-lighter: #f3f0ff;
$interview-success: #22c55e;
$interview-success-light: #f0fdf4;

// 现代化颜色系统
$gray-900: #1f2937;
$gray-800: #374151;
$gray-700: #4b5563;
$gray-600: #6b7280;
$gray-500: #9ca3af;
$gray-400: #d1d5db;
$gray-300: #e5e7eb;
$gray-200: #f3f4f6;
$gray-100: #f8f9fa;
$gray-50: #f9fafb;

// 文本颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;

// Interview文本颜色
$text-dark: #1f2937;
$text-medium: #374151;
$text-light: #6b7280;

// 边框颜色
$border-color: #dcdfe6;
$border-light: #e4e7ed;
$border-lighter: #ebeef5;
$border-extra-light: #f2f6fc;

// Interview边框颜色
$border-gray: #e5e7eb;

// 背景颜色
$background-color: #f5f7fa;
$background-light: #fafafa;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;

// 字体大小
$font-size-extra-small: 10px;
$font-size-small: 12px;
$font-size-base: 14px;
$font-size-medium: 16px;
$font-size-large: 18px;
$font-size-extra-large: 20px;

// 圆角
$border-radius: 4px;
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-large: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// 阴影
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$box-shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);

// 布局
$header-height: 60px;
$sidebar-width: 250px;

// z-index
$z-index-normal: 1;
$z-index-top: 1000;
$z-index-popper: 2000;

// 响应式断点
$sm: 768px;
$md: 992px;
$lg: 1200px;
$xl: 1920px; 